﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.expenseAccountCodes="11200|NA|NA|NA|NA|NA|NA"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="InOutArgument(x:String)" />
    <x:Property Name="company" Type="OutArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="expenseAccountCodes" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="vendorName" Type="OutArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="vendorId" Type="InOutArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="GLCode" Type="InArgument(x:String)" />
    <x:Property Name="correlationID" Type="OutArgument(x:String)" />
    <x:Property Name="InYear" Type="OutArgument(x:String)" />
    <x:Property Name="AYear" Type="OutArgument(x:String)" />
    <x:Property Name="approvalList" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="APResp" Type="InArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="ListOcrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="issueLines" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.Globalization</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode0" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj0" />
      <Variable x:TypeArguments="njl:JToken" Name="out0" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
      <Variable x:TypeArguments="x:String" Name="cuam" />
      <Variable x:TypeArguments="x:String" Name="sino" />
      <Variable x:TypeArguments="x:String" Name="ivdate" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode2" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj2" />
      <Variable x:TypeArguments="x:String" Name="amt" />
      <Variable x:TypeArguments="s:String[]" Name="expenseCodeList" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="invoiceExistsOcrWorkflowOutput" />
      <Variable x:TypeArguments="x:Int32" Name="invoiceExistsOcrWorkflowStatus" />
      <Variable x:TypeArguments="x:Boolean" Name="InvoiceAlreadyExists" />
      <Variable x:TypeArguments="njl:JToken" Name="out1" />
      <Variable x:TypeArguments="x:String" Name="inbnValue2" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
      <Variable x:TypeArguments="x:String" Name="countryCode" />
    </Sequence.Variables>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_372">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[issueLines]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">
          <Literal x:TypeArguments="x:String" Value="" />
        </InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_166">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[vendorId.ToUpper()]</InArgument>
      </Assign.Value>
    </Assign>
    <Sequence DisplayName="Fiscal year Sequence" sap2010:WorkflowViewState.IdRef="Sequence_49">
      <Sequence.Variables>
        <Variable x:TypeArguments="iru:ResponseObject" Name="respForFiscalYear" />
        <Variable x:TypeArguments="x:Int32" Name="respForFiscalYearCode" />
      </Sequence.Variables>
      <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_112">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").Tostring,"dd/MM/yyyy",Nothing).ToString("yyyyMMdd")]</InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[InYear]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
        </Assign.Value>
      </Assign>
      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get Fiscal year IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_22" Response="[respForFiscalYear]" StatusCode="[respForFiscalYearCode]" Url="[tenantID+&quot;M3/m3api-rest/v2/execute/CRS900MI/GetSysCalDate&quot;]">
        <iai:IONAPIRequestWizard.Headers>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>Accept</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>application/json</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.Headers>
        <iai:IONAPIRequestWizard.QueryParameters>
          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>YMD8</x:String>
              <x:String>DIVI</x:String>
            </scg:List>
            <scg:List x:TypeArguments="x:String" Capacity="4">
              <x:String>ivdate</x:String>
              <x:String>division</x:String>
            </scg:List>
          </scg:List>
        </iai:IONAPIRequestWizard.QueryParameters>
      </iai:IONAPIRequestWizard>
      <If Condition="[respForFiscalYearCode = 200]" sap2010:WorkflowViewState.IdRef="If_33">
        <If.Then>
          <If Condition="[respForFiscalYear.ReadAsJson(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_32">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_47">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[ivdate.SubString(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_48">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_115">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[respForFiscalYear.ReadAsJson("results")(0)("records")(0)("CYP1").ToString.Substring(0,4)]</InArgument>
                  </Assign.Value>
                </Assign>
              </Sequence>
            </If.Else>
          </If>
        </If.Then>
      </If>
    </Sequence>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[miscValues("company").ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_65">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[division]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[DictOcrValues(&quot;SUBTOTAL&quot;).Tostring = &quot;&quot; OR DictOcrValues(&quot;SUBTOTAL&quot;).Tostring= &quot;0&quot;]" sap2010:WorkflowViewState.IdRef="If_57">
      <If.Then>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_165">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL")]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Object">[DictOcrValues("TOTAL").Tostring]</InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;SupInvoiceNo&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).Tostring},{&quot;division&quot;,division},{&quot;AYear&quot;,AYear},{&quot;vendorID&quot;,vendorID}}]" ContinueOnError="True" DisplayName="VerifyInvoiceExists Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_3" OutputArguments="[invoiceExistsOcrWorkflowOutput]" ResponseCode="[invoiceExistsOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\VerifyInvoiceExists.xaml&quot;]" />
    <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
      <Assign.To>
        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:Boolean">[CType(invoiceExistsOcrWorkflowOutput("invoiceExist"),boolean)]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_20">
      <If.Then>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_30">
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for vendor name" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[respObj0]" StatusCode="[StatusCode0]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS620MI/GetBasicData&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="8">
                  <x:String>extendedresult</x:String>
                  <x:String>format</x:String>
                  <x:String>righttrim</x:String>
                  <x:String>excludeempty</x:String>
                  <x:String>dateformat</x:String>
                  <x:String>SUNO</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="8">
                  <x:String>false</x:String>
                  <x:String>PRETTY</x:String>
                  <x:String>true</x:String>
                  <x:String>false</x:String>
                  <x:String>YMD8</x:String>
                  <x:String>vendorId</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[StatusCode0 = 200]" sap2010:WorkflowViewState.IdRef="If_1">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out0]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj0.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[out0(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_2">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="x:String" Name="geoc" />
                      </Sequence.Variables>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CONO").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("TEPY").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("PYME").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_370">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[countryCode]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CSCD").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("CUCD").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("SUNM").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[geoc]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("GEOC").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[authUser.ToUpper() = &quot;SUPPLIERAUTHUSER&quot;]" sap2010:WorkflowViewState.IdRef="If_3">
                          <If.Then>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_371">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[authUser]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[out0("results")(0)("records")(0)("RESP").ToString]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                        </If>
                      </Sequence>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="inbnValue" />
                          <Variable x:TypeArguments="x:String" Name="SupAcho" />
                          <Variable x:TypeArguments="x:Int32" Name="StatusCode4" />
                          <Variable x:TypeArguments="iru:ResponseObject" Name="respObj4" />
                          <Variable x:TypeArguments="njl:JToken" Name="out4" />
                          <Variable x:TypeArguments="x:String" Name="bkid" />
                          <Variable x:TypeArguments="s:DateTime" Name="ivdate1" />
                          <Variable x:TypeArguments="x:Int32" Name="respout1" />
                        </Sequence.Variables>
                        <Assign DisplayName="Assign SupAcho" sap2010:WorkflowViewState.IdRef="Assign_43">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[SupAcho]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">["ACHO:"+vendorId + " AND BKTP: 03"]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for bkid" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_6" Response="[respObj4]" StatusCode="[StatusCode4]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/CRS692MI/SearchBnkAcc&quot;]">
                          <iai:IONAPIRequestWizard.Headers>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>Accept</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>application/json</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.Headers>
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>SQRY</x:String>
                                <x:String>dateformat</x:String>
                                <x:String>excludeempty</x:String>
                                <x:String>righttrim</x:String>
                                <x:String>format</x:String>
                                <x:String>extendedresult</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="8">
                                <x:String>SupAcho</x:String>
                                <x:String>YMD8</x:String>
                                <x:String>false</x:String>
                                <x:String>true</x:String>
                                <x:String>PRETTY</x:String>
                                <x:String>false</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                        <If Condition="[StatusCode4 = 200]" sap2010:WorkflowViewState.IdRef="If_11">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_21">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj4.ReadAsText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString &lt;&gt; &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_10">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_19">
                                    <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_40">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(out4("results")(0)("records")(0)("BKID")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_20">
                                    <Assign DisplayName="Assign bkid" sap2010:WorkflowViewState.IdRef="Assign_41">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[bkid]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">
                                          <Literal x:TypeArguments="x:String" Value="" />
                                        </InArgument>
                                      </Assign.Value>
                                    </Assign>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Then>
                        </If>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[cuam]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[DictOcrValues("TOTAL").Tostring]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").Tostring]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="Assign ivdate" sap2010:WorkflowViewState.IdRef="Assign_64">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[ivdate]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[DateTime.ParseExact(DictOcrValues("INVOICE_RECEIPT_DATE").Tostring, "dd/MM/yyyy", Nothing).ToString("yyyyMMdd")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_70">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring + " -"+ DictOcrValues("INVOICE_RECEIPT_ID").Tostring + "-" + division + "-" + vendorID]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                          <TryCatch.Try>
                            <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_110">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[correlationID.Substring(0,35)]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </TryCatch.Try>
                          <TryCatch.Catches>
                            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                              <ActivityAction x:TypeArguments="s:Exception">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                </ActivityAction.Argument>
                                <Assign DisplayName="Assign coID" sap2010:WorkflowViewState.IdRef="Assign_111">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[correlationID]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </ActivityAction>
                            </Catch>
                          </TryCatch.Catches>
                        </TryCatch>
                        <If Condition="[miscValues(&quot;mandateGEOC&quot;).ToString.tolower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_23">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_32">
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_15" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SUNO</x:String>
                                      <x:String>IVDT</x:String>
                                      <x:String>DIVI</x:String>
                                      <x:String>SINO</x:String>
                                      <x:String>CUCD</x:String>
                                      <x:String>TEPY</x:String>
                                      <x:String>PYME</x:String>
                                      <x:String>CUAM</x:String>
                                      <x:String>IMCD</x:String>
                                      <x:String>CRTP</x:String>
                                      <x:String>dateformat</x:String>
                                      <x:String>excludeempty</x:String>
                                      <x:String>righttrim</x:String>
                                      <x:String>format</x:String>
                                      <x:String>extendedresult</x:String>
                                      <x:String>APCD</x:String>
                                      <x:String>GEOC</x:String>
                                      <x:String>CORI</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>vendorId</x:String>
                                      <x:String>ivdate</x:String>
                                      <x:String>division</x:String>
                                      <x:String>sino</x:String>
                                      <x:String>cucd</x:String>
                                      <x:String>tepy</x:String>
                                      <x:String>pyme</x:String>
                                      <x:String>cuam</x:String>
                                      <x:String>0</x:String>
                                      <x:String>1</x:String>
                                      <x:String>YMD8</x:String>
                                      <x:String>false</x:String>
                                      <x:String>true</x:String>
                                      <x:String>PRETTY</x:String>
                                      <x:String>false</x:String>
                                      <x:String>authUser</x:String>
                                      <x:String>geoc</x:String>
                                      <x:String>correlationID</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                              <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_24">
                                <If.Then>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_45" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>APCD</x:String>
                                          <x:String>BKID</x:String>
                                          <x:String>GEOC</x:String>
                                          <x:String>CORI</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>vendorId</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>0</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>authUser</x:String>
                                          <x:String>bkid</x:String>
                                          <x:String>geoc</x:String>
                                          <x:String>correlationID</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_34">
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_17" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>SUNO</x:String>
                                      <x:String>IVDT</x:String>
                                      <x:String>DIVI</x:String>
                                      <x:String>SINO</x:String>
                                      <x:String>CUCD</x:String>
                                      <x:String>TEPY</x:String>
                                      <x:String>PYME</x:String>
                                      <x:String>CUAM</x:String>
                                      <x:String>IMCD</x:String>
                                      <x:String>CRTP</x:String>
                                      <x:String>dateformat</x:String>
                                      <x:String>excludeempty</x:String>
                                      <x:String>righttrim</x:String>
                                      <x:String>format</x:String>
                                      <x:String>extendedresult</x:String>
                                      <x:String>APCD</x:String>
                                      <x:String>CORI</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="32">
                                      <x:String>vendorId</x:String>
                                      <x:String>ivdate</x:String>
                                      <x:String>division</x:String>
                                      <x:String>sino</x:String>
                                      <x:String>cucd</x:String>
                                      <x:String>tepy</x:String>
                                      <x:String>pyme</x:String>
                                      <x:String>cuam</x:String>
                                      <x:String>0</x:String>
                                      <x:String>1</x:String>
                                      <x:String>YMD8</x:String>
                                      <x:String>false</x:String>
                                      <x:String>true</x:String>
                                      <x:String>PRETTY</x:String>
                                      <x:String>false</x:String>
                                      <x:String>authUser</x:String>
                                      <x:String>correlationID</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                              <If Condition="[StatusCode1 = 200  and respObj1.ReadAsText.Contains(&quot;Bank account identity must be entered&quot;)]" sap2010:WorkflowViewState.IdRef="If_25">
                                <If.Then>
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Invoice header" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_46" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHead&quot;]">
                                    <iai:IONAPIRequestWizard.Headers>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>Accept</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>application/json</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.Headers>
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>SUNO</x:String>
                                          <x:String>IVDT</x:String>
                                          <x:String>DIVI</x:String>
                                          <x:String>SINO</x:String>
                                          <x:String>CUCD</x:String>
                                          <x:String>TEPY</x:String>
                                          <x:String>PYME</x:String>
                                          <x:String>CUAM</x:String>
                                          <x:String>IMCD</x:String>
                                          <x:String>CRTP</x:String>
                                          <x:String>dateformat</x:String>
                                          <x:String>excludeempty</x:String>
                                          <x:String>righttrim</x:String>
                                          <x:String>format</x:String>
                                          <x:String>extendedresult</x:String>
                                          <x:String>APCD</x:String>
                                          <x:String>BKID</x:String>
                                          <x:String>CORI</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="32">
                                          <x:String>vendorId</x:String>
                                          <x:String>ivdate</x:String>
                                          <x:String>division</x:String>
                                          <x:String>sino</x:String>
                                          <x:String>cucd</x:String>
                                          <x:String>tepy</x:String>
                                          <x:String>pyme</x:String>
                                          <x:String>cuam</x:String>
                                          <x:String>0</x:String>
                                          <x:String>1</x:String>
                                          <x:String>YMD8</x:String>
                                          <x:String>false</x:String>
                                          <x:String>true</x:String>
                                          <x:String>PRETTY</x:String>
                                          <x:String>false</x:String>
                                          <x:String>authUser</x:String>
                                          <x:String>bkid</x:String>
                                          <x:String>correlationID</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Else>
                        </If>
                        <If Condition="[StatusCode1 = 200]" sap2010:WorkflowViewState.IdRef="If_5">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_10">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="x:String" Name="inyr" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj1.ReadAsText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_4">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("errorMessage")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_35" Line="[commentStatus]" Source="[logfile]" />
                                  </Sequence>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:Boolean">[InvoiceAlreadyExists]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_18">
                                      <Assign.To>
                                        <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                      </Assign.To>
                                      <Assign.Value>
                                        <InArgument x:TypeArguments="x:String">["Invoice Header Created"]</InArgument>
                                      </Assign.Value>
                                    </Assign>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[commentStatus]" Source="[logfile]" />
                                  </Sequence>
                                </If.Else>
                              </If>
                              <If Condition="[NOT InvoiceAlreadyExists]" sap2010:WorkflowViewState.IdRef="If_6">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_12">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="x:String" Name="accRule" />
                                      <Variable x:TypeArguments="x:String" Name="productGr" />
                                      <Variable x:TypeArguments="x:String" Name="orderNo" />
                                      <Variable x:TypeArguments="x:String" Name="partnerCo" />
                                      <Variable x:TypeArguments="x:String" Name="finGrp" />
                                      <Variable x:TypeArguments="x:String" Name="costCtr" />
                                      <Variable x:TypeArguments="x:String" Name="account" />
                                      <Variable x:TypeArguments="x:String" Name="AIT1" />
                                      <Variable x:TypeArguments="x:String" Name="AIT2" />
                                      <Variable x:TypeArguments="x:String" Name="AIT3" />
                                      <Variable x:TypeArguments="x:String" Name="AIT4" />
                                      <Variable x:TypeArguments="x:String" Name="AIT5" />
                                      <Variable x:TypeArguments="x:String" Name="AIT6" />
                                      <Variable x:TypeArguments="x:String" Name="AIT7" />
                                    </Sequence.Variables>
                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="validate1 IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_26" Response="[respObj2]" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                      <iai:IONAPIRequestWizard.Headers>
                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>Accept</x:String>
                                          </scg:List>
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>application/json</x:String>
                                          </scg:List>
                                        </scg:List>
                                      </iai:IONAPIRequestWizard.Headers>
                                      <iai:IONAPIRequestWizard.QueryParameters>
                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>INBN</x:String>
                                            <x:String>DIVI</x:String>
                                          </scg:List>
                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                            <x:String>inbnValue</x:String>
                                            <x:String>division</x:String>
                                          </scg:List>
                                        </scg:List>
                                      </iai:IONAPIRequestWizard.QueryParameters>
                                    </iai:IONAPIRequestWizard>
                                    <If Condition="[respout1 = 200]" sap2010:WorkflowViewState.IdRef="If_58">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_72">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="x:Int32" Name="loopBreak" />
                                            <Variable x:TypeArguments="x:String" Name="supa" />
                                            <Variable x:TypeArguments="iru:ResponseObject" Name="GetHeadRespObj" />
                                            <Variable x:TypeArguments="x:Int32" Name="ValidateStatus" />
                                            <Variable x:TypeArguments="x:Int32" Name="getVoucherStatus" />
                                          </Sequence.Variables>
                                          <DoWhile sap2010:WorkflowViewState.IdRef="DoWhile_1">
                                            <DoWhile.Variables>
                                              <Variable x:TypeArguments="x:String" Name="bist" />
                                            </DoWhile.Variables>
                                            <DoWhile.Condition>[bist &lt;&gt; "0" AND supa = "50" AND loopBreak &lt;10]</DoWhile.Condition>
                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_74">
                                              <Sequence.Variables>
                                                <Variable x:TypeArguments="x:Int32" Name="loopBreak1" />
                                                <Variable x:TypeArguments="njl:JToken" Name="variable1" />
                                              </Sequence.Variables>
                                              <Delay Duration="00:00:02" sap2010:WorkflowViewState.IdRef="Delay_1" />
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Check the satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_27" Response="[GetHeadRespObj]" StatusCode="[ValidateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/GetHead&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>INBN</x:String>
                                                      <x:String>DIVI</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>division</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_167">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[supa]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("SUPA").ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_168">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:Int32">[loopBreak1]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:Int32">1</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <While sap2010:WorkflowViewState.IdRef="While_1" Condition="[supa = &quot;10&quot; And loopbreak1 &lt; 5]">
                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_73">
                                                  <Delay Duration="00:00:02" sap2010:WorkflowViewState.IdRef="Delay_2" />
                                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Check the satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_28" Response="[GetHeadRespObj]" StatusCode="[ValidateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/GetHead&quot;]">
                                                    <iai:IONAPIRequestWizard.Headers>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>Accept</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>application/json</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.Headers>
                                                    <iai:IONAPIRequestWizard.QueryParameters>
                                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>INBN</x:String>
                                                          <x:String>DIVI</x:String>
                                                        </scg:List>
                                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                                          <x:String>inbnValue</x:String>
                                                          <x:String>division</x:String>
                                                        </scg:List>
                                                      </scg:List>
                                                    </iai:IONAPIRequestWizard.QueryParameters>
                                                  </iai:IONAPIRequestWizard>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:String">[supa]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("SUPA").ToString]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                                                    <Assign.To>
                                                      <OutArgument x:TypeArguments="x:Int32">[loopBreak1]</OutArgument>
                                                    </Assign.To>
                                                    <Assign.Value>
                                                      <InArgument x:TypeArguments="x:Int32">[loopBreak1 + 1]</InArgument>
                                                    </Assign.Value>
                                                  </Assign>
                                                </Sequence>
                                              </While>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_171">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[bist]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("BIST").ToString]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_15" Line="[&quot;Validation status iterations: &quot; + loopbreak.ToString + &quot;  bist: &quot; + bist + &quot; supa: &quot; + supa]" Source="[logfile]" />
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:Int32">[loopBreak]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:Int32">[loopBreak + 1]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </Sequence>
                                          </DoWhile>
                                          <Delay Duration="00:00:02" sap2010:WorkflowViewState.IdRef="Delay_3" />
                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Check the satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_29" Response="[GetHeadRespObj]" StatusCode="[ValidateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/GetHead&quot;]">
                                            <iai:IONAPIRequestWizard.Headers>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>Accept</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>application/json</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.Headers>
                                            <iai:IONAPIRequestWizard.QueryParameters>
                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>INBN</x:String>
                                                  <x:String>DIVI</x:String>
                                                </scg:List>
                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                  <x:String>inbnValue</x:String>
                                                  <x:String>division</x:String>
                                                </scg:List>
                                              </scg:List>
                                            </iai:IONAPIRequestWizard.QueryParameters>
                                          </iai:IONAPIRequestWizard>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[supa]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("SUPA").ToString]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_176">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[DictOcrValues("INVOICE_RECEIPT_ID").Tostring.ToUpper]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_177">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[inyr]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[ivdate.Substring(0,4)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_30" Line="Validate1 completed" Source="[logfile]" />
                                          <If Condition="[supa = &quot;90&quot;]" sap2010:WorkflowViewState.IdRef="If_59">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_76">
                                                <Sequence.Variables>
                                                  <Variable x:TypeArguments="x:String" Name="vser" />
                                                </Sequence.Variables>
                                                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Get the voucher satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_30" Response="[GetHeadRespObj]" StatusCode="[getVoucherStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS200MI/GetInvTotInfo&quot;]">
                                                  <iai:IONAPIRequestWizard.Headers>
                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                        <x:String>Accept</x:String>
                                                      </scg:List>
                                                      <scg:List x:TypeArguments="x:String" Capacity="4">
                                                        <x:String>application/json</x:String>
                                                      </scg:List>
                                                    </scg:List>
                                                  </iai:IONAPIRequestWizard.Headers>
                                                  <iai:IONAPIRequestWizard.QueryParameters>
                                                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                        <x:String>DIVI</x:String>
                                                        <x:String>SPYN</x:String>
                                                        <x:String>SUNO</x:String>
                                                        <x:String>SINO</x:String>
                                                        <x:String>INYR</x:String>
                                                      </scg:List>
                                                      <scg:List x:TypeArguments="x:String" Capacity="8">
                                                        <x:String>division</x:String>
                                                        <x:String>vendorId</x:String>
                                                        <x:String>vendorId</x:String>
                                                        <x:String>sino</x:String>
                                                        <x:String>inyr</x:String>
                                                      </scg:List>
                                                    </scg:List>
                                                  </iai:IONAPIRequestWizard.QueryParameters>
                                                </iai:IONAPIRequestWizard>
                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_31" Line="Get voucher details with first Inbn value" Source="[logfile]" />
                                                <If Condition="[getVoucherStatus = 200]" sap2010:WorkflowViewState.IdRef="If_60">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_77">
                                                      <Sequence.Variables>
                                                        <Variable x:TypeArguments="x:String" Name="vono" />
                                                        <Variable x:TypeArguments="x:String" Name="yea4" />
                                                        <Variable x:TypeArguments="x:String" Name="acdt" />
                                                      </Sequence.Variables>
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_178">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(GetHeadRespObj.ReadAsText)]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                      <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_61">
                                                        <If.Then>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_145">
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_337">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[(out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_38" Line="[commentStatus]" Source="[logfile]" />
                                                          </Sequence>
                                                        </If.Then>
                                                        <If.Else>
                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_81">
                                                            <Sequence.Variables>
                                                              <Variable x:TypeArguments="iru:ResponseObject" Name="AccountingvoucherResp" />
                                                              <Variable x:TypeArguments="njl:JToken" Name="out3" />
                                                              <Variable x:TypeArguments="x:String" Name="jrno" />
                                                            </Sequence.Variables>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_186">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[vono]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("VONO")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_187">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[yea4]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("YEA4")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_188">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[acdt]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("ACDT")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_189">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[vser]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("VSER")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_191">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[jrno]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("JRNO")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_190">
                                                              <Assign.To>
                                                                <OutArgument x:TypeArguments="x:String">[vser]</OutArgument>
                                                              </Assign.To>
                                                              <Assign.Value>
                                                                <InArgument x:TypeArguments="x:String">[(out1("results")(0)("records")(0)("VSER")).ToString]</InArgument>
                                                              </Assign.Value>
                                                            </Assign>
                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="GLS200get voucher IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_33" Response="[AccountingvoucherResp]" StatusCode="[getVoucherStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/GLS200MI/GetVoucherLine&quot;]">
                                                              <iai:IONAPIRequestWizard.Headers>
                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                    <x:String>Accept</x:String>
                                                                  </scg:List>
                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                    <x:String>application/json</x:String>
                                                                  </scg:List>
                                                                </scg:List>
                                                              </iai:IONAPIRequestWizard.Headers>
                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                    <x:String>DIVI</x:String>
                                                                    <x:String>VONO</x:String>
                                                                    <x:String>JSNO</x:String>
                                                                    <x:String>JRNO</x:String>
                                                                    <x:String>YEA4</x:String>
                                                                    <x:String>VSER</x:String>
                                                                  </scg:List>
                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                    <x:String>division</x:String>
                                                                    <x:String>vono</x:String>
                                                                    <x:String>2</x:String>
                                                                    <x:String>jrno</x:String>
                                                                    <x:String>yea4</x:String>
                                                                    <x:String>vser</x:String>
                                                                  </scg:List>
                                                                </scg:List>
                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                            </iai:IONAPIRequestWizard>
                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_32" Line="Get accounting lines for the validated line" Source="[logfile]" />
                                                            <If Condition="[getVoucherStatus = 200]" sap2010:WorkflowViewState.IdRef="If_64">
                                                              <If.Then>
                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_82">
                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_185">
                                                                    <Assign.To>
                                                                      <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
                                                                    </Assign.To>
                                                                    <Assign.Value>
                                                                      <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(AccountingvoucherResp.ReadAsText)]</InArgument>
                                                                    </Assign.Value>
                                                                  </Assign>
                                                                  <If Condition="[out3(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_65">
                                                                    <If.Then>
                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_146">
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_338">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(out3("results")(0)("errorMessage")).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_39" Line="[commentStatus]" Source="[logfile]" />
                                                                      </Sequence>
                                                                    </If.Then>
                                                                    <If.Else>
                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_83">
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_197">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT1")).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_196">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT2")).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_195">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT3")).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_194">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT4")).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_198">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT5")).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_199">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT6")).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_200">
                                                                          <Assign.To>
                                                                            <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                          </Assign.To>
                                                                          <Assign.Value>
                                                                            <InArgument x:TypeArguments="x:String">[(out3("results")(0)("records")(0)("AIT7")).ToString]</InArgument>
                                                                          </Assign.Value>
                                                                        </Assign>
                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_78">
                                                                          <Sequence.Variables>
                                                                            <Variable x:TypeArguments="x:Int32" Name="addHeadRecodeStatus" />
                                                                            <Variable x:TypeArguments="njl:JToken" Name="out2" />
                                                                            <Variable x:TypeArguments="iru:ResponseObject" Name="AddHeadRespObj" />
                                                                          </Sequence.Variables>
                                                                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="AddHeadRecode IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_31" Response="[AddHeadRespObj]" StatusCode="[addHeadRecodeStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/AddHeadRecode&quot;]">
                                                                            <iai:IONAPIRequestWizard.Headers>
                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>Accept</x:String>
                                                                                </scg:List>
                                                                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                  <x:String>application/json</x:String>
                                                                                </scg:List>
                                                                              </scg:List>
                                                                            </iai:IONAPIRequestWizard.Headers>
                                                                            <iai:IONAPIRequestWizard.QueryParameters>
                                                                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                  <x:String>DIVI</x:String>
                                                                                  <x:String>VONO</x:String>
                                                                                  <x:String>ACDT</x:String>
                                                                                  <x:String>IMCD</x:String>
                                                                                  <x:String>YEA4</x:String>
                                                                                  <x:String>VSER</x:String>
                                                                                </scg:List>
                                                                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                  <x:String>division</x:String>
                                                                                  <x:String>vono</x:String>
                                                                                  <x:String>acdt</x:String>
                                                                                  <x:String>0</x:String>
                                                                                  <x:String>yea4</x:String>
                                                                                  <x:String>vser</x:String>
                                                                                </scg:List>
                                                                              </scg:List>
                                                                            </iai:IONAPIRequestWizard.QueryParameters>
                                                                          </iai:IONAPIRequestWizard>
                                                                          <If Condition="[addHeadRecodeStatus = 200]" sap2010:WorkflowViewState.IdRef="If_62">
                                                                            <If.Then>
                                                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_79">
                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_183">
                                                                                  <Assign.To>
                                                                                    <OutArgument x:TypeArguments="njl:JToken">[out2]</OutArgument>
                                                                                  </Assign.To>
                                                                                  <Assign.Value>
                                                                                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(AddHeadRespObj.ReadAsText)]</InArgument>
                                                                                  </Assign.Value>
                                                                                </Assign>
                                                                                <If Condition="[out2(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_63">
                                                                                  <If.Then>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_147">
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_339">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[(out2("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_40" Line="[commentStatus]" Source="[logfile]" />
                                                                                    </Sequence>
                                                                                  </If.Then>
                                                                                  <If.Else>
                                                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_80">
                                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_33" Line="Recoded Header Added" Source="[logfile]" />
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_184">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[inbnValue2]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">[(out2("results")(0)("records")(0)("INBN")).ToString]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_202">
                                                                                        <Assign.To>
                                                                                          <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                                        </Assign.To>
                                                                                        <Assign.Value>
                                                                                          <InArgument x:TypeArguments="x:String">["-"+DictOcrValues("TOTAL").Tostring]</InArgument>
                                                                                        </Assign.Value>
                                                                                      </Assign>
                                                                                      <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding recode lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_34" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLineRecode&quot;]">
                                                                                        <iai:IONAPIRequestWizard.Headers>
                                                                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                              <x:String>Accept</x:String>
                                                                                            </scg:List>
                                                                                            <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                              <x:String>application/json</x:String>
                                                                                            </scg:List>
                                                                                          </scg:List>
                                                                                        </iai:IONAPIRequestWizard.Headers>
                                                                                        <iai:IONAPIRequestWizard.QueryParameters>
                                                                                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                            <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                              <x:String>INBN</x:String>
                                                                                              <x:String>DIVI</x:String>
                                                                                              <x:String>NLAM</x:String>
                                                                                              <x:String>AIT1</x:String>
                                                                                              <x:String>AIT2</x:String>
                                                                                              <x:String>AIT3</x:String>
                                                                                              <x:String>AIT4</x:String>
                                                                                              <x:String>AIT5</x:String>
                                                                                              <x:String>AIT6</x:String>
                                                                                              <x:String>AIT7</x:String>
                                                                                            </scg:List>
                                                                                            <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                              <x:String>inbnValue2</x:String>
                                                                                              <x:String>division</x:String>
                                                                                              <x:String>amt</x:String>
                                                                                              <x:String>AIT1</x:String>
                                                                                              <x:String>AIT2</x:String>
                                                                                              <x:String>AIT3</x:String>
                                                                                              <x:String>AIT4</x:String>
                                                                                              <x:String>AIT5</x:String>
                                                                                              <x:String>AIT6</x:String>
                                                                                              <x:String>AIT7</x:String>
                                                                                            </scg:List>
                                                                                          </scg:List>
                                                                                        </iai:IONAPIRequestWizard.QueryParameters>
                                                                                      </iai:IONAPIRequestWizard>
                                                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_34" Line="Recoded Line Added" Source="[logfile]" />
                                                                                      <If Condition="[StatusCode2 = 200]" sap2010:WorkflowViewState.IdRef="If_66">
                                                                                        <If.Then>
                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_84">
                                                                                            <Sequence.Variables>
                                                                                              <Variable x:TypeArguments="x:String" Name="acqt" />
                                                                                            </Sequence.Variables>
                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_203">
                                                                                              <Assign.To>
                                                                                                <OutArgument x:TypeArguments="njl:JToken">[out4]</OutArgument>
                                                                                              </Assign.To>
                                                                                              <Assign.Value>
                                                                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                                              </Assign.Value>
                                                                                            </Assign>
                                                                                            <If Condition="[out4(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_67">
                                                                                              <If.Then>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_148">
                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_340">
                                                                                                    <Assign.To>
                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                    </Assign.To>
                                                                                                    <Assign.Value>
                                                                                                      <InArgument x:TypeArguments="x:String">[(out4("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                                    </Assign.Value>
                                                                                                  </Assign>
                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_41" Line="[commentStatus]" Source="[logfile]" />
                                                                                                </Sequence>
                                                                                              </If.Then>
                                                                                              <If.Else>
                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_143">
                                                                                                  <If Condition="[GLCode = &quot;DISTRIBUTED&quot; AND ListocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_117">
                                                                                                    <If.Then>
                                                                                                      <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_16" Values="[ListocrLineValues]">
                                                                                                        <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                                                                                          <ActivityAction.Argument>
                                                                                                            <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                                                                                                          </ActivityAction.Argument>
                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_117">
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_368">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_369">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_267">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_268">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_269">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_270">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_271">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_272">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_273">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_331">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("QUANTITY").ToString]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <If Condition="[acqt.trim = &quot;&quot; OR acqt.trim = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_120">
                                                                                                              <If.Then>
                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_332">
                                                                                                                  <Assign.To>
                                                                                                                    <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                                                                  </Assign.To>
                                                                                                                  <Assign.Value>
                                                                                                                    <InArgument x:TypeArguments="x:String">
                                                                                                                      <Literal x:TypeArguments="x:String" Value="" />
                                                                                                                    </InArgument>
                                                                                                                  </Assign.Value>
                                                                                                                </Assign>
                                                                                                              </If.Then>
                                                                                                            </If>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_274">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_40" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLineRecode&quot;]">
                                                                                                              <iai:IONAPIRequestWizard.Headers>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                    <x:String>Accept</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                    <x:String>application/json</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.Headers>
                                                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                    <x:String>INBN</x:String>
                                                                                                                    <x:String>RDTP</x:String>
                                                                                                                    <x:String>DIVI</x:String>
                                                                                                                    <x:String>NLAM</x:String>
                                                                                                                    <x:String>AIT1</x:String>
                                                                                                                    <x:String>AIT2</x:String>
                                                                                                                    <x:String>AIT3</x:String>
                                                                                                                    <x:String>AIT4</x:String>
                                                                                                                    <x:String>AIT5</x:String>
                                                                                                                    <x:String>AIT6</x:String>
                                                                                                                    <x:String>AIT7</x:String>
                                                                                                                    <x:String>ACQT</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                    <x:String>inbnValue2</x:String>
                                                                                                                    <x:String>8</x:String>
                                                                                                                    <x:String>division</x:String>
                                                                                                                    <x:String>amt</x:String>
                                                                                                                    <x:String>AIT1</x:String>
                                                                                                                    <x:String>AIT2</x:String>
                                                                                                                    <x:String>AIT3</x:String>
                                                                                                                    <x:String>AIT4</x:String>
                                                                                                                    <x:String>AIT5</x:String>
                                                                                                                    <x:String>AIT6</x:String>
                                                                                                                    <x:String>AIT7</x:String>
                                                                                                                    <x:String>acqt</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            </iai:IONAPIRequestWizard>
                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_275">
                                                                                                              <Assign.To>
                                                                                                                <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                                                                              </Assign.To>
                                                                                                              <Assign.Value>
                                                                                                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                                                              </Assign.Value>
                                                                                                            </Assign>
                                                                                                            <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_94">
                                                                                                              <If.Then>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_115">
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_276">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_277">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_23" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                </Sequence>
                                                                                                              </If.Then>
                                                                                                              <If.Else>
                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_116">
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_278">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_279">
                                                                                                                    <Assign.To>
                                                                                                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                    </Assign.To>
                                                                                                                    <Assign.Value>
                                                                                                                      <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                                                    </Assign.Value>
                                                                                                                  </Assign>
                                                                                                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_24" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                </Sequence>
                                                                                                              </If.Else>
                                                                                                            </If>
                                                                                                          </Sequence>
                                                                                                        </ActivityAction>
                                                                                                      </ForEach>
                                                                                                    </If.Then>
                                                                                                    <If.Else>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_141">
                                                                                                        <If Condition="[GLCode = &quot;DISTRIBUTED&quot;]" sap2010:WorkflowViewState.IdRef="If_116">
                                                                                                          <If.Then>
                                                                                                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_25" Line="[commentStatus]" Source="[logfile]" />
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_140">
                                                                                                              <If Condition="[ListocrLineValues.Count&gt;0]" DisplayName="If GLCode did not obtain from excep" sap2010:WorkflowViewState.IdRef="If_112">
                                                                                                                <If.Then>
                                                                                                                  <If Condition="[ListocrLineValues(0)(&quot;DESCRIPTION&quot;).Tostring.Contains(&quot;~&quot;)]" sap2010:WorkflowViewState.IdRef="If_103">
                                                                                                                    <If.Else>
                                                                                                                      <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_102">
                                                                                                                        <If.Then>
                                                                                                                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_125">
                                                                                                                            <Sequence.Variables>
                                                                                                                              <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                                                                                              <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                                                                                              <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                                                                                            </Sequence.Variables>
                                                                                                                            <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_12" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                                                                                            <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_97">
                                                                                                                              <If.Then>
                                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_120">
                                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_280">
                                                                                                                                    <Assign.To>
                                                                                                                                      <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                                                                    </Assign.To>
                                                                                                                                    <Assign.Value>
                                                                                                                                      <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                                                                                    </Assign.Value>
                                                                                                                                  </Assign>
                                                                                                                                  <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_96">
                                                                                                                                    <If.Then>
                                                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_119">
                                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_281">
                                                                                                                                          <Assign.To>
                                                                                                                                            <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                                                          </Assign.To>
                                                                                                                                          <Assign.Value>
                                                                                                                                            <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                                                          </Assign.Value>
                                                                                                                                        </Assign>
                                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_282">
                                                                                                                                          <Assign.To>
                                                                                                                                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                                                          </Assign.To>
                                                                                                                                          <Assign.Value>
                                                                                                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                                                          </Assign.Value>
                                                                                                                                        </Assign>
                                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_283">
                                                                                                                                          <Assign.To>
                                                                                                                                            <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                                          </Assign.To>
                                                                                                                                          <Assign.Value>
                                                                                                                                            <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                                                          </Assign.Value>
                                                                                                                                        </Assign>
                                                                                                                                        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_10">
                                                                                                                                          <TryCatch.Try>
                                                                                                                                            <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_95">
                                                                                                                                              <If.Then>
                                                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_118">
                                                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_346">
                                                                                                                                                    <Assign.To>
                                                                                                                                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                                                                    </Assign.To>
                                                                                                                                                    <Assign.Value>
                                                                                                                                                      <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                    </Assign.Value>
                                                                                                                                                  </Assign>
                                                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_347">
                                                                                                                                                    <Assign.To>
                                                                                                                                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                                                                    </Assign.To>
                                                                                                                                                    <Assign.Value>
                                                                                                                                                      <InArgument x:TypeArguments="x:Object">[GLCode]</InArgument>
                                                                                                                                                    </Assign.Value>
                                                                                                                                                  </Assign>
                                                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_348">
                                                                                                                                                    <Assign.To>
                                                                                                                                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                                                                    </Assign.To>
                                                                                                                                                    <Assign.Value>
                                                                                                                                                      <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                    </Assign.Value>
                                                                                                                                                  </Assign>
                                                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_349">
                                                                                                                                                    <Assign.To>
                                                                                                                                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                                                                    </Assign.To>
                                                                                                                                                    <Assign.Value>
                                                                                                                                                      <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                    </Assign.Value>
                                                                                                                                                  </Assign>
                                                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_350">
                                                                                                                                                    <Assign.To>
                                                                                                                                                      <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                                                                    </Assign.To>
                                                                                                                                                    <Assign.Value>
                                                                                                                                                      <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                                                                    </Assign.Value>
                                                                                                                                                  </Assign>
                                                                                                                                                  <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_54" MethodName="Add">
                                                                                                                                                    <InvokeMethod.TargetObject>
                                                                                                                                                      <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                                                                    </InvokeMethod.TargetObject>
                                                                                                                                                    <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                                                                  </InvokeMethod>
                                                                                                                                                </Sequence>
                                                                                                                                              </If.Then>
                                                                                                                                            </If>
                                                                                                                                          </TryCatch.Try>
                                                                                                                                          <TryCatch.Catches>
                                                                                                                                            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_10">
                                                                                                                                              <ActivityAction x:TypeArguments="s:Exception">
                                                                                                                                                <ActivityAction.Argument>
                                                                                                                                                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                                                                </ActivityAction.Argument>
                                                                                                                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_284">
                                                                                                                                                  <Assign.To>
                                                                                                                                                    <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                                                  </Assign.To>
                                                                                                                                                  <Assign.Value>
                                                                                                                                                    <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                                                                  </Assign.Value>
                                                                                                                                                </Assign>
                                                                                                                                              </ActivityAction>
                                                                                                                                            </Catch>
                                                                                                                                          </TryCatch.Catches>
                                                                                                                                        </TryCatch>
                                                                                                                                      </Sequence>
                                                                                                                                    </If.Then>
                                                                                                                                  </If>
                                                                                                                                </Sequence>
                                                                                                                              </If.Then>
                                                                                                                            </If>
                                                                                                                            <If Condition="[vendorResponseCode &lt;&gt; 200 OR GLCode = &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_101">
                                                                                                                              <If.Then>
                                                                                                                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_124">
                                                                                                                                  <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_13" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                                                                                  <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_100">
                                                                                                                                    <If.Then>
                                                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_123">
                                                                                                                                        <Sequence.Variables>
                                                                                                                                          <Variable x:TypeArguments="x:String" Name="comb" />
                                                                                                                                        </Sequence.Variables>
                                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_285">
                                                                                                                                          <Assign.To>
                                                                                                                                            <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                                                                                          </Assign.To>
                                                                                                                                          <Assign.Value>
                                                                                                                                            <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                                                                                          </Assign.Value>
                                                                                                                                        </Assign>
                                                                                                                                        <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_99">
                                                                                                                                          <If.Then>
                                                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_122">
                                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_286">
                                                                                                                                                <Assign.To>
                                                                                                                                                  <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                                                </Assign.To>
                                                                                                                                                <Assign.Value>
                                                                                                                                                  <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                                                                </Assign.Value>
                                                                                                                                              </Assign>
                                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_287">
                                                                                                                                                <Assign.To>
                                                                                                                                                  <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                                                                                </Assign.To>
                                                                                                                                                <Assign.Value>
                                                                                                                                                  <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                                                                                                </Assign.Value>
                                                                                                                                              </Assign>
                                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_288">
                                                                                                                                                <Assign.To>
                                                                                                                                                  <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                                                                </Assign.To>
                                                                                                                                                <Assign.Value>
                                                                                                                                                  <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                                                                </Assign.Value>
                                                                                                                                              </Assign>
                                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_289">
                                                                                                                                                <Assign.To>
                                                                                                                                                  <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                                                                </Assign.To>
                                                                                                                                                <Assign.Value>
                                                                                                                                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                                                                </Assign.Value>
                                                                                                                                              </Assign>
                                                                                                                                              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_11">
                                                                                                                                                <TryCatch.Try>
                                                                                                                                                  <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_98">
                                                                                                                                                    <If.Then>
                                                                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_121">
                                                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_351">
                                                                                                                                                          <Assign.To>
                                                                                                                                                            <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                                                                          </Assign.To>
                                                                                                                                                          <Assign.Value>
                                                                                                                                                            <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                          </Assign.Value>
                                                                                                                                                        </Assign>
                                                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_352">
                                                                                                                                                          <Assign.To>
                                                                                                                                                            <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                                                                          </Assign.To>
                                                                                                                                                          <Assign.Value>
                                                                                                                                                            <InArgument x:TypeArguments="x:Object">[comb]</InArgument>
                                                                                                                                                          </Assign.Value>
                                                                                                                                                        </Assign>
                                                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_353">
                                                                                                                                                          <Assign.To>
                                                                                                                                                            <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                                                                          </Assign.To>
                                                                                                                                                          <Assign.Value>
                                                                                                                                                            <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                          </Assign.Value>
                                                                                                                                                        </Assign>
                                                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_354">
                                                                                                                                                          <Assign.To>
                                                                                                                                                            <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                                                                          </Assign.To>
                                                                                                                                                          <Assign.Value>
                                                                                                                                                            <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                          </Assign.Value>
                                                                                                                                                        </Assign>
                                                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_355">
                                                                                                                                                          <Assign.To>
                                                                                                                                                            <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                                                                          </Assign.To>
                                                                                                                                                          <Assign.Value>
                                                                                                                                                            <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                                                                          </Assign.Value>
                                                                                                                                                        </Assign>
                                                                                                                                                        <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_60" MethodName="Add">
                                                                                                                                                          <InvokeMethod.TargetObject>
                                                                                                                                                            <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                                                                          </InvokeMethod.TargetObject>
                                                                                                                                                          <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                                                                        </InvokeMethod>
                                                                                                                                                      </Sequence>
                                                                                                                                                    </If.Then>
                                                                                                                                                  </If>
                                                                                                                                                </TryCatch.Try>
                                                                                                                                                <TryCatch.Catches>
                                                                                                                                                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_11">
                                                                                                                                                    <ActivityAction x:TypeArguments="s:Exception">
                                                                                                                                                      <ActivityAction.Argument>
                                                                                                                                                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                                                                      </ActivityAction.Argument>
                                                                                                                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_290">
                                                                                                                                                        <Assign.To>
                                                                                                                                                          <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                                                        </Assign.To>
                                                                                                                                                        <Assign.Value>
                                                                                                                                                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                                                                        </Assign.Value>
                                                                                                                                                      </Assign>
                                                                                                                                                    </ActivityAction>
                                                                                                                                                  </Catch>
                                                                                                                                                </TryCatch.Catches>
                                                                                                                                              </TryCatch>
                                                                                                                                            </Sequence>
                                                                                                                                          </If.Then>
                                                                                                                                        </If>
                                                                                                                                      </Sequence>
                                                                                                                                    </If.Then>
                                                                                                                                  </If>
                                                                                                                                </Sequence>
                                                                                                                              </If.Then>
                                                                                                                            </If>
                                                                                                                          </Sequence>
                                                                                                                        </If.Then>
                                                                                                                      </If>
                                                                                                                    </If.Else>
                                                                                                                  </If>
                                                                                                                </If.Then>
                                                                                                                <If.Else>
                                                                                                                  <If Condition="[vendorId &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_111">
                                                                                                                    <If.Then>
                                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_133">
                                                                                                                        <Sequence.Variables>
                                                                                                                          <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
                                                                                                                          <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
                                                                                                                          <Variable x:TypeArguments="x:String" Name="invoiceType" />
                                                                                                                        </Sequence.Variables>
                                                                                                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;company&quot;,company},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="SupplierInfo Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_14" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\SupplierInfo.xaml&quot;]" />
                                                                                                                        <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_106">
                                                                                                                          <If.Then>
                                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_128">
                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_291">
                                                                                                                                <Assign.To>
                                                                                                                                  <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                                                                </Assign.To>
                                                                                                                                <Assign.Value>
                                                                                                                                  <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("invoiceType"), String)]</InArgument>
                                                                                                                                </Assign.Value>
                                                                                                                              </Assign>
                                                                                                                              <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_105">
                                                                                                                                <If.Then>
                                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_127">
                                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_292">
                                                                                                                                      <Assign.To>
                                                                                                                                        <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                                                      </Assign.To>
                                                                                                                                      <Assign.Value>
                                                                                                                                        <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                                                      </Assign.Value>
                                                                                                                                    </Assign>
                                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_293">
                                                                                                                                      <Assign.To>
                                                                                                                                        <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                                                      </Assign.To>
                                                                                                                                      <Assign.Value>
                                                                                                                                        <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                                                      </Assign.Value>
                                                                                                                                    </Assign>
                                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_294">
                                                                                                                                      <Assign.To>
                                                                                                                                        <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                                      </Assign.To>
                                                                                                                                      <Assign.Value>
                                                                                                                                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                                                      </Assign.Value>
                                                                                                                                    </Assign>
                                                                                                                                    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_12">
                                                                                                                                      <TryCatch.Try>
                                                                                                                                        <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_104">
                                                                                                                                          <If.Then>
                                                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_126">
                                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_363">
                                                                                                                                                <Assign.To>
                                                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                                                                </Assign.To>
                                                                                                                                                <Assign.Value>
                                                                                                                                                  <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                </Assign.Value>
                                                                                                                                              </Assign>
                                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_364">
                                                                                                                                                <Assign.To>
                                                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                                                                </Assign.To>
                                                                                                                                                <Assign.Value>
                                                                                                                                                  <InArgument x:TypeArguments="x:Object">[GLCode]</InArgument>
                                                                                                                                                </Assign.Value>
                                                                                                                                              </Assign>
                                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_365">
                                                                                                                                                <Assign.To>
                                                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                                                                </Assign.To>
                                                                                                                                                <Assign.Value>
                                                                                                                                                  <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                </Assign.Value>
                                                                                                                                              </Assign>
                                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_366">
                                                                                                                                                <Assign.To>
                                                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                                                                </Assign.To>
                                                                                                                                                <Assign.Value>
                                                                                                                                                  <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                </Assign.Value>
                                                                                                                                              </Assign>
                                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_367">
                                                                                                                                                <Assign.To>
                                                                                                                                                  <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                                                                </Assign.To>
                                                                                                                                                <Assign.Value>
                                                                                                                                                  <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                                                                </Assign.Value>
                                                                                                                                              </Assign>
                                                                                                                                              <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_66" MethodName="Add">
                                                                                                                                                <InvokeMethod.TargetObject>
                                                                                                                                                  <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                                                                </InvokeMethod.TargetObject>
                                                                                                                                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                                                              </InvokeMethod>
                                                                                                                                            </Sequence>
                                                                                                                                          </If.Then>
                                                                                                                                        </If>
                                                                                                                                      </TryCatch.Try>
                                                                                                                                      <TryCatch.Catches>
                                                                                                                                        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_12">
                                                                                                                                          <ActivityAction x:TypeArguments="s:Exception">
                                                                                                                                            <ActivityAction.Argument>
                                                                                                                                              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                                                            </ActivityAction.Argument>
                                                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_295">
                                                                                                                                              <Assign.To>
                                                                                                                                                <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                                              </Assign.To>
                                                                                                                                              <Assign.Value>
                                                                                                                                                <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("GLCode"), String)]</InArgument>
                                                                                                                                              </Assign.Value>
                                                                                                                                            </Assign>
                                                                                                                                          </ActivityAction>
                                                                                                                                        </Catch>
                                                                                                                                      </TryCatch.Catches>
                                                                                                                                    </TryCatch>
                                                                                                                                  </Sequence>
                                                                                                                                </If.Then>
                                                                                                                              </If>
                                                                                                                            </Sequence>
                                                                                                                          </If.Then>
                                                                                                                        </If>
                                                                                                                        <If Condition="[vendorResponseCode &lt;&gt; 200 OR NOT ListOcrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_110">
                                                                                                                          <If.Then>
                                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_132">
                                                                                                                              <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorID&quot;,vendorID},{&quot;Division&quot;,division},{&quot;businessRule&quot;,miscValues(&quot;businessRuleForDivisionGLCode&quot;).ToString}}]" ContinueOnError="True" DisplayName="DivisiontoGLCode Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_15" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\DivisiontoGLCode.xaml&quot;]" />
                                                                                                                              <If Condition="[vendorResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_109">
                                                                                                                                <If.Then>
                                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_131">
                                                                                                                                    <Sequence.Variables>
                                                                                                                                      <Variable x:TypeArguments="x:String" Name="comb" />
                                                                                                                                    </Sequence.Variables>
                                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_296">
                                                                                                                                      <Assign.To>
                                                                                                                                        <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                                                                                                                                      </Assign.To>
                                                                                                                                      <Assign.Value>
                                                                                                                                        <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("comb"), String)]</InArgument>
                                                                                                                                      </Assign.Value>
                                                                                                                                    </Assign>
                                                                                                                                    <If Condition="[comb &lt;&gt; &quot;&quot;]" sap2010:WorkflowViewState.IdRef="If_108">
                                                                                                                                      <If.Then>
                                                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_130">
                                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_297">
                                                                                                                                            <Assign.To>
                                                                                                                                              <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                                            </Assign.To>
                                                                                                                                            <Assign.Value>
                                                                                                                                              <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                                                            </Assign.Value>
                                                                                                                                          </Assign>
                                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_298">
                                                                                                                                            <Assign.To>
                                                                                                                                              <OutArgument x:TypeArguments="x:String">[invoiceType]</OutArgument>
                                                                                                                                            </Assign.To>
                                                                                                                                            <Assign.Value>
                                                                                                                                              <InArgument x:TypeArguments="x:String">Expense Invoice</InArgument>
                                                                                                                                            </Assign.Value>
                                                                                                                                          </Assign>
                                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_299">
                                                                                                                                            <Assign.To>
                                                                                                                                              <OutArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</OutArgument>
                                                                                                                                            </Assign.To>
                                                                                                                                            <Assign.Value>
                                                                                                                                              <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[New List(Of Dictionary(Of String, Object))]</InArgument>
                                                                                                                                            </Assign.Value>
                                                                                                                                          </Assign>
                                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
                                                                                                                                            <Assign.To>
                                                                                                                                              <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                                                            </Assign.To>
                                                                                                                                            <Assign.Value>
                                                                                                                                              <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String, Object)]</InArgument>
                                                                                                                                            </Assign.Value>
                                                                                                                                          </Assign>
                                                                                                                                          <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_13">
                                                                                                                                            <TryCatch.Try>
                                                                                                                                              <If Condition="[invoiceType.ToLower.contains(&quot;non po&quot;) OR invoiceType.ToLower.contains(&quot;expense&quot;)]" sap2010:WorkflowViewState.IdRef="If_107">
                                                                                                                                                <If.Then>
                                                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_129">
                                                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_358">
                                                                                                                                                      <Assign.To>
                                                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("SUPPLIER_ITEM_CODE")]</OutArgument>
                                                                                                                                                      </Assign.To>
                                                                                                                                                      <Assign.Value>
                                                                                                                                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                      </Assign.Value>
                                                                                                                                                    </Assign>
                                                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_359">
                                                                                                                                                      <Assign.To>
                                                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("Dimensions")]</OutArgument>
                                                                                                                                                      </Assign.To>
                                                                                                                                                      <Assign.Value>
                                                                                                                                                        <InArgument x:TypeArguments="x:Object">[comb]</InArgument>
                                                                                                                                                      </Assign.Value>
                                                                                                                                                    </Assign>
                                                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_360">
                                                                                                                                                      <Assign.To>
                                                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("QUANTITY")]</OutArgument>
                                                                                                                                                      </Assign.To>
                                                                                                                                                      <Assign.Value>
                                                                                                                                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                      </Assign.Value>
                                                                                                                                                    </Assign>
                                                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_361">
                                                                                                                                                      <Assign.To>
                                                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("UNIT_PRICE")]</OutArgument>
                                                                                                                                                      </Assign.To>
                                                                                                                                                      <Assign.Value>
                                                                                                                                                        <InArgument x:TypeArguments="x:Object">[""]</InArgument>
                                                                                                                                                      </Assign.Value>
                                                                                                                                                    </Assign>
                                                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_362">
                                                                                                                                                      <Assign.To>
                                                                                                                                                        <OutArgument x:TypeArguments="x:Object">[LinesDict("LINE_AMOUNT")]</OutArgument>
                                                                                                                                                      </Assign.To>
                                                                                                                                                      <Assign.Value>
                                                                                                                                                        <InArgument x:TypeArguments="x:Object">[DictOcrValues("SUBTOTAL").Tostring]</InArgument>
                                                                                                                                                      </Assign.Value>
                                                                                                                                                    </Assign>
                                                                                                                                                    <InvokeMethod DisplayName="Assign to ocrValueLines InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_72" MethodName="Add">
                                                                                                                                                      <InvokeMethod.TargetObject>
                                                                                                                                                        <InArgument x:TypeArguments="scg:List(scg:Dictionary(x:String, x:Object))">[ListocrLineValues]</InArgument>
                                                                                                                                                      </InvokeMethod.TargetObject>
                                                                                                                                                      <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</InArgument>
                                                                                                                                                    </InvokeMethod>
                                                                                                                                                  </Sequence>
                                                                                                                                                </If.Then>
                                                                                                                                              </If>
                                                                                                                                            </TryCatch.Try>
                                                                                                                                            <TryCatch.Catches>
                                                                                                                                              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_13">
                                                                                                                                                <ActivityAction x:TypeArguments="s:Exception">
                                                                                                                                                  <ActivityAction.Argument>
                                                                                                                                                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                                                                                                                                  </ActivityAction.Argument>
                                                                                                                                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_301">
                                                                                                                                                    <Assign.To>
                                                                                                                                                      <OutArgument x:TypeArguments="x:String">[GLCode]</OutArgument>
                                                                                                                                                    </Assign.To>
                                                                                                                                                    <Assign.Value>
                                                                                                                                                      <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("AIT1"), String)]</InArgument>
                                                                                                                                                    </Assign.Value>
                                                                                                                                                  </Assign>
                                                                                                                                                </ActivityAction>
                                                                                                                                              </Catch>
                                                                                                                                            </TryCatch.Catches>
                                                                                                                                          </TryCatch>
                                                                                                                                        </Sequence>
                                                                                                                                      </If.Then>
                                                                                                                                    </If>
                                                                                                                                  </Sequence>
                                                                                                                                </If.Then>
                                                                                                                              </If>
                                                                                                                            </Sequence>
                                                                                                                          </If.Then>
                                                                                                                        </If>
                                                                                                                      </Sequence>
                                                                                                                    </If.Then>
                                                                                                                  </If>
                                                                                                                </If.Else>
                                                                                                              </If>
                                                                                                              <If Condition="[ListocrLineValues.Count &gt; 0]" sap2010:WorkflowViewState.IdRef="If_115">
                                                                                                                <If.Then>
                                                                                                                  <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ForEach&lt;Dictionary&lt;String,Object&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_15" Values="[ListocrLineValues]">
                                                                                                                    <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                                                                                                                      <ActivityAction.Argument>
                                                                                                                        <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item" />
                                                                                                                      </ActivityAction.Argument>
                                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_136">
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_356">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary (Of String, Object)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_357">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_302">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(0)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_303">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(1)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_304">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(2)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_305">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(3)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_306">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(4)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_307">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(5)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_308">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("DESCRIPTION").ToString.Split("~"c)(6)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_333">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("QUANTITY").ToString]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <If Condition="[acqt.trim = &quot;&quot; OR acqt.trim = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_121">
                                                                                                                          <If.Then>
                                                                                                                            <Assign sap2010:WorkflowViewState.IdRef="Assign_334">
                                                                                                                              <Assign.To>
                                                                                                                                <OutArgument x:TypeArguments="x:String">[acqt]</OutArgument>
                                                                                                                              </Assign.To>
                                                                                                                              <Assign.Value>
                                                                                                                                <InArgument x:TypeArguments="x:String">
                                                                                                                                  <Literal x:TypeArguments="x:String" Value="" />
                                                                                                                                </InArgument>
                                                                                                                              </Assign.Value>
                                                                                                                            </Assign>
                                                                                                                          </If.Then>
                                                                                                                        </If>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_309">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="x:String">[LinesDict("LINE_AMOUNT").ToString]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_41" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLineRecode&quot;]">
                                                                                                                          <iai:IONAPIRequestWizard.Headers>
                                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                                <x:String>Accept</x:String>
                                                                                                                              </scg:List>
                                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                                <x:String>application/json</x:String>
                                                                                                                              </scg:List>
                                                                                                                            </scg:List>
                                                                                                                          </iai:IONAPIRequestWizard.Headers>
                                                                                                                          <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                                <x:String>INBN</x:String>
                                                                                                                                <x:String>RDTP</x:String>
                                                                                                                                <x:String>DIVI</x:String>
                                                                                                                                <x:String>NLAM</x:String>
                                                                                                                                <x:String>AIT1</x:String>
                                                                                                                                <x:String>AIT2</x:String>
                                                                                                                                <x:String>AIT3</x:String>
                                                                                                                                <x:String>AIT4</x:String>
                                                                                                                                <x:String>AIT5</x:String>
                                                                                                                                <x:String>AIT6</x:String>
                                                                                                                                <x:String>AIT7</x:String>
                                                                                                                                <x:String>ACQT</x:String>
                                                                                                                              </scg:List>
                                                                                                                              <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                                <x:String>inbnValue2</x:String>
                                                                                                                                <x:String>8</x:String>
                                                                                                                                <x:String>division</x:String>
                                                                                                                                <x:String>amt</x:String>
                                                                                                                                <x:String>AIT1</x:String>
                                                                                                                                <x:String>AIT2</x:String>
                                                                                                                                <x:String>AIT3</x:String>
                                                                                                                                <x:String>AIT4</x:String>
                                                                                                                                <x:String>AIT5</x:String>
                                                                                                                                <x:String>AIT6</x:String>
                                                                                                                                <x:String>AIT7</x:String>
                                                                                                                                <x:String>acqt</x:String>
                                                                                                                              </scg:List>
                                                                                                                            </scg:List>
                                                                                                                          </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                        </iai:IONAPIRequestWizard>
                                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_310">
                                                                                                                          <Assign.To>
                                                                                                                            <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                                                                                          </Assign.To>
                                                                                                                          <Assign.Value>
                                                                                                                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                                                                          </Assign.Value>
                                                                                                                        </Assign>
                                                                                                                        <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_113">
                                                                                                                          <If.Then>
                                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_134">
                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_311">
                                                                                                                                <Assign.To>
                                                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                                </Assign.To>
                                                                                                                                <Assign.Value>
                                                                                                                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                                                                </Assign.Value>
                                                                                                                              </Assign>
                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_312">
                                                                                                                                <Assign.To>
                                                                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                                </Assign.To>
                                                                                                                                <Assign.Value>
                                                                                                                                  <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                                                </Assign.Value>
                                                                                                                              </Assign>
                                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_26" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                            </Sequence>
                                                                                                                          </If.Then>
                                                                                                                          <If.Else>
                                                                                                                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_135">
                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_313">
                                                                                                                                <Assign.To>
                                                                                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                                </Assign.To>
                                                                                                                                <Assign.Value>
                                                                                                                                  <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                                                                                </Assign.Value>
                                                                                                                              </Assign>
                                                                                                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_314">
                                                                                                                                <Assign.To>
                                                                                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                                </Assign.To>
                                                                                                                                <Assign.Value>
                                                                                                                                  <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                                                                </Assign.Value>
                                                                                                                              </Assign>
                                                                                                                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_27" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                            </Sequence>
                                                                                                                          </If.Else>
                                                                                                                        </If>
                                                                                                                      </Sequence>
                                                                                                                    </ActivityAction>
                                                                                                                  </ForEach>
                                                                                                                </If.Then>
                                                                                                                <If.Else>
                                                                                                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_139">
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_315">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">[GLCode]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_316">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                                                        </InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_317">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                                                        </InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_318">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                                                        </InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_319">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                                                        </InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_320">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                                                        </InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_321">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">
                                                                                                                          <Literal x:TypeArguments="x:String" Value="" />
                                                                                                                        </InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_322">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="x:String">[amt]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="x:String">[LinesDict("UNIT_PRICE").ToString]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding lines" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_42" Response="[respObj2]" StatusCode="[StatusCode2]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                                      <iai:IONAPIRequestWizard.Headers>
                                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                            <x:String>Accept</x:String>
                                                                                                                          </scg:List>
                                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                            <x:String>application/json</x:String>
                                                                                                                          </scg:List>
                                                                                                                        </scg:List>
                                                                                                                      </iai:IONAPIRequestWizard.Headers>
                                                                                                                      <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                            <x:String>INBN</x:String>
                                                                                                                            <x:String>RDTP</x:String>
                                                                                                                            <x:String>DIVI</x:String>
                                                                                                                            <x:String>NLAM</x:String>
                                                                                                                            <x:String>AIT1</x:String>
                                                                                                                            <x:String>AIT2</x:String>
                                                                                                                            <x:String>AIT3</x:String>
                                                                                                                            <x:String>AIT4</x:String>
                                                                                                                            <x:String>AIT5</x:String>
                                                                                                                            <x:String>AIT6</x:String>
                                                                                                                            <x:String>AIT7</x:String>
                                                                                                                          </scg:List>
                                                                                                                          <scg:List x:TypeArguments="x:String" Capacity="16">
                                                                                                                            <x:String>inbnValue</x:String>
                                                                                                                            <x:String>8</x:String>
                                                                                                                            <x:String>division</x:String>
                                                                                                                            <x:String>amt</x:String>
                                                                                                                            <x:String>AIT1</x:String>
                                                                                                                            <x:String>AIT2</x:String>
                                                                                                                            <x:String>AIT3</x:String>
                                                                                                                            <x:String>AIT4</x:String>
                                                                                                                            <x:String>AIT5</x:String>
                                                                                                                            <x:String>AIT6</x:String>
                                                                                                                            <x:String>AIT7</x:String>
                                                                                                                          </scg:List>
                                                                                                                        </scg:List>
                                                                                                                      </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                    </iai:IONAPIRequestWizard>
                                                                                                                    <Assign sap2010:WorkflowViewState.IdRef="Assign_323">
                                                                                                                      <Assign.To>
                                                                                                                        <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                                                                                                                      </Assign.To>
                                                                                                                      <Assign.Value>
                                                                                                                        <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj2.ReadAsText)]</InArgument>
                                                                                                                      </Assign.Value>
                                                                                                                    </Assign>
                                                                                                                    <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" sap2010:WorkflowViewState.IdRef="If_114">
                                                                                                                      <If.Then>
                                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_137">
                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_324">
                                                                                                                            <Assign.To>
                                                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                            </Assign.To>
                                                                                                                            <Assign.Value>
                                                                                                                              <InArgument x:TypeArguments="x:String" xml:space="preserve">["Error occured while adding the line.  "  +  (out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                                                                                            </Assign.Value>
                                                                                                                          </Assign>
                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_325">
                                                                                                                            <Assign.To>
                                                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                            </Assign.To>
                                                                                                                            <Assign.Value>
                                                                                                                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                                                                                                            </Assign.Value>
                                                                                                                          </Assign>
                                                                                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_28" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                        </Sequence>
                                                                                                                      </If.Then>
                                                                                                                      <If.Else>
                                                                                                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_138">
                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_326">
                                                                                                                            <Assign.To>
                                                                                                                              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                                                                            </Assign.To>
                                                                                                                            <Assign.Value>
                                                                                                                              <InArgument x:TypeArguments="x:String">["Invoice Line created"]</InArgument>
                                                                                                                            </Assign.Value>
                                                                                                                          </Assign>
                                                                                                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_327">
                                                                                                                            <Assign.To>
                                                                                                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                                                                            </Assign.To>
                                                                                                                            <Assign.Value>
                                                                                                                              <InArgument x:TypeArguments="x:String">["SUCCESS"]</InArgument>
                                                                                                                            </Assign.Value>
                                                                                                                          </Assign>
                                                                                                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_29" Line="[commentStatus]" Source="[logfile]" />
                                                                                                                        </Sequence>
                                                                                                                      </If.Else>
                                                                                                                    </If>
                                                                                                                  </Sequence>
                                                                                                                </If.Else>
                                                                                                              </If>
                                                                                                            </Sequence>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Else>
                                                                                                  </If>
                                                                                                  <If Condition="[CInt(DictOcrValues(&quot;VAT_AMOUNT&quot;).Tostring) &lt;&gt; 0]" sap2010:WorkflowViewState.IdRef="If_119">
                                                                                                    <If.Then>
                                                                                                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_142">
                                                                                                        <Sequence.Variables>
                                                                                                          <Variable x:TypeArguments="x:String" Name="vat" />
                                                                                                          <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
                                                                                                          <Variable x:TypeArguments="x:String" Name="vatCode" />
                                                                                                        </Sequence.Variables>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_328">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[vat]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").Tostring]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_329">
                                                                                                          <Assign.To>
                                                                                                            <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
                                                                                                          </Assign.To>
                                                                                                          <Assign.Value>
                                                                                                            <InArgument x:TypeArguments="x:String">[miscValues("vatCodeConfig").ToString]</InArgument>
                                                                                                          </Assign.Value>
                                                                                                        </Assign>
                                                                                                        <If Condition="[miscValues(&quot;vatCodeConfig&quot;).ToString = &quot;NA&quot;]" sap2010:WorkflowViewState.IdRef="If_118">
                                                                                                          <If.Then>
                                                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_43" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                              <iai:IONAPIRequestWizard.Headers>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                    <x:String>Accept</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                    <x:String>application/json</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.Headers>
                                                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                    <x:String>INBN</x:String>
                                                                                                                    <x:String>RDTP</x:String>
                                                                                                                    <x:String>DIVI</x:String>
                                                                                                                    <x:String>GLAM</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                    <x:String>inbnValue2</x:String>
                                                                                                                    <x:String>3</x:String>
                                                                                                                    <x:String>division</x:String>
                                                                                                                    <x:String>vat</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            </iai:IONAPIRequestWizard>
                                                                                                          </If.Then>
                                                                                                          <If.Else>
                                                                                                            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request for Adding VAT" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_44" Response="[respout1]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                                                                                                              <iai:IONAPIRequestWizard.Headers>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                    <x:String>Accept</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="4">
                                                                                                                    <x:String>application/json</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.Headers>
                                                                                                              <iai:IONAPIRequestWizard.QueryParameters>
                                                                                                                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>INBN</x:String>
                                                                                                                    <x:String>RDTP</x:String>
                                                                                                                    <x:String>DIVI</x:String>
                                                                                                                    <x:String>VTA1</x:String>
                                                                                                                    <x:String>VTCD</x:String>
                                                                                                                  </scg:List>
                                                                                                                  <scg:List x:TypeArguments="x:String" Capacity="8">
                                                                                                                    <x:String>inbnValue2</x:String>
                                                                                                                    <x:String>3</x:String>
                                                                                                                    <x:String>division</x:String>
                                                                                                                    <x:String>vat</x:String>
                                                                                                                    <x:String>vatCode</x:String>
                                                                                                                  </scg:List>
                                                                                                                </scg:List>
                                                                                                              </iai:IONAPIRequestWizard.QueryParameters>
                                                                                                            </iai:IONAPIRequestWizard>
                                                                                                          </If.Else>
                                                                                                        </If>
                                                                                                      </Sequence>
                                                                                                    </If.Then>
                                                                                                  </If>
                                                                                                </Sequence>
                                                                                              </If.Else>
                                                                                            </If>
                                                                                          </Sequence>
                                                                                        </If.Then>
                                                                                      </If>
                                                                                    </Sequence>
                                                                                  </If.Else>
                                                                                </If>
                                                                              </Sequence>
                                                                            </If.Then>
                                                                          </If>
                                                                        </Sequence>
                                                                      </Sequence>
                                                                    </If.Else>
                                                                  </If>
                                                                </Sequence>
                                                              </If.Then>
                                                            </If>
                                                          </Sequence>
                                                        </If.Else>
                                                      </If>
                                                    </Sequence>
                                                  </If.Then>
                                                </If>
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_150">
                                                <Assign sap2010:WorkflowViewState.IdRef="Assign_335">
                                                  <Assign.To>
                                                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                  </Assign.To>
                                                  <Assign.Value>
                                                    <InArgument x:TypeArguments="x:String">[(out1("results")(0)("errorMessage")).ToString]</InArgument>
                                                  </Assign.Value>
                                                </Assign>
                                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_36" Line="[commentStatus]" Source="[logfile]" />
                                              </Sequence>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </If.Then>
                              </If>
                              <If Condition="[inbnValue2 = &quot;&quot;]" DisplayName="If validation" sap2010:WorkflowViewState.IdRef="If_15">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_52">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                                <If.Else>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_25">
                                    <Sequence.Variables>
                                      <Variable x:TypeArguments="x:String" Name="SuccessMoved" />
                                    </Sequence.Variables>
                                    <If Condition="[approvalRequired]" sap2010:WorkflowViewState.IdRef="If_18">
                                      <If.Then>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_28">
                                          <Sequence.Variables>
                                            <Variable x:TypeArguments="x:Boolean" Name="approvalRequest" />
                                            <Variable x:TypeArguments="x:Int32" Name="sendApprovalResponseCode" />
                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="checkApprovalResponseDictionary" />
                                            <Variable x:TypeArguments="x:Int32" Name="checkApprovalResponseCode" />
                                            <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="sendApprovalResponseDictionary" />
                                          </Sequence.Variables>
                                          <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,DictOcrValues(&quot;TOTAL&quot;).Tostring},{&quot;tenantID&quot;,tenantID},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" OutputArguments="[checkApprovalResponseDictionary]" ResponseCode="[checkApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\CheckApproval.xaml&quot;]" />
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_57">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:Boolean">[approvalRequest]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:Boolean">[CType(checkApprovalResponseDictionary("approvalRequest"), Boolean)]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <If Condition="[approvalRequest]" sap2010:WorkflowViewState.IdRef="If_17">
                                            <If.Then>
                                              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_27">
                                                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;amount&quot;,DictOcrValues(&quot;TOTAL&quot;).Tostring},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,DIVISION},{&quot;inbn&quot;,inbnValue},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).Tostring},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).Tostring},{&quot;vendorName&quot;,vendorId},{&quot;approvalWorkflow&quot;,approvalWorkflow}}]" ContinueOnError="True" DisplayName="Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" OutputArguments="[sendApprovalResponseDictionary]" ResponseCode="[sendApprovalResponseCode]" WorkflowFile="[projectPath+&quot;\SendtoApprovalWorkflow.xaml&quot;]" />
                                                <If Condition="[sendApprovalResponseCode = 200]" sap2010:WorkflowViewState.IdRef="If_16">
                                                  <If.Then>
                                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_26">
                                                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="Supllier invoice is sent for approval since the invoice amount is large" Source="[logfile]" />
                                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_58">
                                                        <Assign.To>
                                                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                        </Assign.To>
                                                        <Assign.Value>
                                                          <InArgument x:TypeArguments="x:String">["Invoice created and Sent for approval"]</InArgument>
                                                        </Assign.Value>
                                                      </Assign>
                                                    </Sequence>
                                                  </If.Then>
                                                </If>
                                              </Sequence>
                                            </If.Then>
                                            <If.Else>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" Response="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>INBN</x:String>
                                                      <x:String>DIVI</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>inbnValue</x:String>
                                                      <x:String>division</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                            </If.Else>
                                          </If>
                                        </Sequence>
                                      </If.Then>
                                      <If.Else>
                                        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_42">
                                          <If Condition="[miscValues(&quot;AutomateValidation&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="If_34">
                                            <If.Then>
                                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respObj2]" StatusCode="[respout1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS455MI/ValidByBatchNo&quot;]">
                                                <iai:IONAPIRequestWizard.Headers>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>Accept</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>application/json</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.Headers>
                                                <iai:IONAPIRequestWizard.QueryParameters>
                                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>INBN</x:String>
                                                      <x:String>DIVI</x:String>
                                                    </scg:List>
                                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                                      <x:String>inbnValue2</x:String>
                                                      <x:String>division</x:String>
                                                    </scg:List>
                                                  </scg:List>
                                                </iai:IONAPIRequestWizard.QueryParameters>
                                              </iai:IONAPIRequestWizard>
                                            </If.Then>
                                          </If>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_96">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">SUCCESS</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                        </Sequence>
                                      </If.Else>
                                    </If>
                                    <If Condition="[respOut1 = 200]" sap2010:WorkflowViewState.IdRef="If_19">
                                      <If.Then>
                                        <Assign sap2010:WorkflowViewState.IdRef="Assign_59">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">["Invoice created and validated"]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </If.Then>
                                    </If>
                                  </Sequence>
                                </If.Else>
                              </If>
                            </Sequence>
                          </If.Then>
                          <If.Else>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_11">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_19">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["Received an IONAPI error while adding Invoice header."]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_20">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[commentStatus]" Source="[logfile]" />
                            </Sequence>
                          </If.Else>
                        </If>
                      </Sequence>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_23">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_49">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[(out0("results")(0)("errorMessage")).ToString]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_50">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching Vendor name for the Vendor Id " + vendorId+"."]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[commentStatus]" Source="[logfile]" />
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_29">
          <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Invoice Number " + DictOcrValues("INVOICE_RECEIPT_ID").Tostring + " already exists."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_372" sap:VirtualizedContainerService.HintSize="6263,60" />
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="6263,60" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="6263,60" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="678,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_22" sap:VirtualizedContainerService.HintSize="678,22" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_47" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_115" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="553,332" />
      <sap2010:ViewStateData Id="If_33" sap:VirtualizedContainerService.HintSize="678,480" />
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="6263,866">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="6263,60" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="6263,60" />
      <sap2010:ViewStateData Id="Assign_165" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_57" sap:VirtualizedContainerService.HintSize="6263,208" />
      <sap2010:ViewStateData Id="InvokeWorkflow_3" sap:VirtualizedContainerService.HintSize="6263,22" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="6263,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="5952,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="5705,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_370" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_371" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="5394,1032">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="5372,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_6" sap:VirtualizedContainerService.HintSize="5372,22" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_20" sap:VirtualizedContainerService.HintSize="264,184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="553,332" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="575,556">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="5372,704">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="5372,60" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="5372,60" />
      <sap2010:ViewStateData Id="Assign_64" sap:VirtualizedContainerService.HintSize="5372,60" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="5372,60" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="5372,287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_15" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_45" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_32" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_17" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_46" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="486,394">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="5372,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="5061,60" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_35" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="5061,594">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_26" sap:VirtualizedContainerService.HintSize="4912,22" />
      <sap2010:ViewStateData Id="Delay_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_27" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_168" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Delay_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_28" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_73" sap:VirtualizedContainerService.HintSize="264,408">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="464,566" />
      <sap2010:ViewStateData Id="Assign_171" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Append_Line_15" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_74" sap:VirtualizedContainerService.HintSize="486,1276">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DoWhile_1" sap:VirtualizedContainerService.HintSize="4765,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Delay_3" sap:VirtualizedContainerService.HintSize="4765,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_29" sap:VirtualizedContainerService.HintSize="4765,22" />
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="4765,60" />
      <sap2010:ViewStateData Id="Assign_176" sap:VirtualizedContainerService.HintSize="4765,60" />
      <sap2010:ViewStateData Id="Assign_177" sap:VirtualizedContainerService.HintSize="4765,60" />
      <sap2010:ViewStateData Id="Append_Line_30" sap:VirtualizedContainerService.HintSize="4765,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_30" sap:VirtualizedContainerService.HintSize="4452.66666666667,22" />
      <sap2010:ViewStateData Id="Append_Line_31" sap:VirtualizedContainerService.HintSize="4452.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_178" sap:VirtualizedContainerService.HintSize="4304.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_337" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_38" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_145" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_186" sap:VirtualizedContainerService.HintSize="3992.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_187" sap:VirtualizedContainerService.HintSize="3992.66666666667,62" />
      <sap2010:ViewStateData Id="Assign_188" sap:VirtualizedContainerService.HintSize="3992.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_189" sap:VirtualizedContainerService.HintSize="3992.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_191" sap:VirtualizedContainerService.HintSize="3992.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_190" sap:VirtualizedContainerService.HintSize="3992.66666666667,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_33" sap:VirtualizedContainerService.HintSize="3992.66666666667,22" />
      <sap2010:ViewStateData Id="Append_Line_32" sap:VirtualizedContainerService.HintSize="3992.66666666667,22" />
      <sap2010:ViewStateData Id="Assign_185" sap:VirtualizedContainerService.HintSize="554,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_338" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_39" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_146" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_197" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_196" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_195" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_194" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_198" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_199" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_200" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_31" sap:VirtualizedContainerService.HintSize="1098,22" />
      <sap2010:ViewStateData Id="Assign_183" sap:VirtualizedContainerService.HintSize="950,62" />
      <sap2010:ViewStateData Id="Assign_339" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_40" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_147" sap:VirtualizedContainerService.HintSize="264,246">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_33" sap:VirtualizedContainerService.HintSize="638,22" />
      <sap2010:ViewStateData Id="Assign_184" sap:VirtualizedContainerService.HintSize="638,62" />
      <sap2010:ViewStateData Id="Assign_202" sap:VirtualizedContainerService.HintSize="638,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_34" sap:VirtualizedContainerService.HintSize="638,22" />
      <sap2010:ViewStateData Id="Append_Line_34" sap:VirtualizedContainerService.HintSize="638,22" />
      <sap2010:ViewStateData Id="Assign_203" sap:VirtualizedContainerService.HintSize="490,62" />
      <sap2010:ViewStateData Id="Assign_340" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_41" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_148" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_368" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_369" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_267" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_268" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_269" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_270" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_271" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_272" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_273" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_331" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_332" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_120" sap:VirtualizedContainerService.HintSize="554,216" />
      <sap2010:ViewStateData Id="Assign_274" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_40" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_275" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_276" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_277" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_23" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_115" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_278" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_279" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_24" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_116" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_94" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_117" sap:VirtualizedContainerService.HintSize="576,2170">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_16" sap:VirtualizedContainerService.HintSize="606.666666666667,2322.66666666667" />
      <sap2010:ViewStateData Id="Append_Line_25" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_12" sap:VirtualizedContainerService.HintSize="926.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_280" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_281" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_282" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_283" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_346" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_347" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_348" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_349" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_350" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_54" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_118" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_95" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_284" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_10" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_10" sap:VirtualizedContainerService.HintSize="482.666666666667,1160" />
      <sap2010:ViewStateData Id="Sequence_119" sap:VirtualizedContainerService.HintSize="504.666666666667,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_96" sap:VirtualizedContainerService.HintSize="630.666666666667,1744">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_120" sap:VirtualizedContainerService.HintSize="652.666666666667,1970">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_97" sap:VirtualizedContainerService.HintSize="926.666666666667,2124" />
      <sap2010:ViewStateData Id="InvokeWorkflow_13" sap:VirtualizedContainerService.HintSize="778.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_285" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_286" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_287" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_288" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_289" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_351" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_352" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_353" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_354" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_355" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_60" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_121" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_98" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_290" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_11" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_11" sap:VirtualizedContainerService.HintSize="482.666666666667,1160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_122" sap:VirtualizedContainerService.HintSize="504.666666666667,1692">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_99" sap:VirtualizedContainerService.HintSize="630.666666666667,1846" />
      <sap2010:ViewStateData Id="Sequence_123" sap:VirtualizedContainerService.HintSize="652.666666666667,2072">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_100" sap:VirtualizedContainerService.HintSize="778.666666666667,2226">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_124" sap:VirtualizedContainerService.HintSize="800.666666666667,2412">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_101" sap:VirtualizedContainerService.HintSize="926.666666666667,2566" />
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="948.666666666667,4916">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_102" sap:VirtualizedContainerService.HintSize="1074.66666666667,5070" />
      <sap2010:ViewStateData Id="If_103" sap:VirtualizedContainerService.HintSize="1200.66666666667,5224" />
      <sap2010:ViewStateData Id="InvokeWorkflow_14" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_291" sap:VirtualizedContainerService.HintSize="630.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_293" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_294" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_363" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_364" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_365" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_366" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_367" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_66" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_126" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_104" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_295" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_12" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_12" sap:VirtualizedContainerService.HintSize="482.666666666667,1160" />
      <sap2010:ViewStateData Id="Sequence_127" sap:VirtualizedContainerService.HintSize="504.666666666667,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_105" sap:VirtualizedContainerService.HintSize="630.666666666667,1744">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_128" sap:VirtualizedContainerService.HintSize="652.666666666667,1970">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_106" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_15" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_296" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_298" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_299" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="482.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_358" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_359" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_360" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_361" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_362" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_72" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Sequence_129" sap:VirtualizedContainerService.HintSize="264,768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_107" sap:VirtualizedContainerService.HintSize="464,922">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_301" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_13" sap:VirtualizedContainerService.HintSize="468.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_13" sap:VirtualizedContainerService.HintSize="482.666666666667,1160">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_130" sap:VirtualizedContainerService.HintSize="504.666666666667,1692">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_108" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_131" sap:VirtualizedContainerService.HintSize="264,278.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_109" sap:VirtualizedContainerService.HintSize="464,432.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_132" sap:VirtualizedContainerService.HintSize="486,618.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_110" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_133" sap:VirtualizedContainerService.HintSize="222,331.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_111" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_112" sap:VirtualizedContainerService.HintSize="2301.33333333333,5378">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_356" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_357" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_302" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_303" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_304" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_305" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_306" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_307" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_308" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_333" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_334" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_121" sap:VirtualizedContainerService.HintSize="554,216" />
      <sap2010:ViewStateData Id="Assign_309" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_41" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_310" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_311" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_312" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_26" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_134" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_313" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_314" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_27" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_135" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_113" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="576,2170">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_15" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_315" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_316" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_317" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_318" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_319" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_320" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_321" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_322" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_42" sap:VirtualizedContainerService.HintSize="554,22" />
      <sap2010:ViewStateData Id="Assign_323" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_324" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_325" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_28" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_137" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_326" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_327" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_29" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_138" sap:VirtualizedContainerService.HintSize="264,350">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_114" sap:VirtualizedContainerService.HintSize="554,504" />
      <sap2010:ViewStateData Id="Sequence_139" sap:VirtualizedContainerService.HintSize="576,1608">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_115" sap:VirtualizedContainerService.HintSize="2301.33333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_140" sap:VirtualizedContainerService.HintSize="2323.33333333333,5594.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_116" sap:VirtualizedContainerService.HintSize="2549.33333333333,5748.66666666667" />
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_117" sap:VirtualizedContainerService.HintSize="832.666666666667,2476.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_328" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_329" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_43" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_44" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_118" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Sequence_142" sap:VirtualizedContainerService.HintSize="486,542">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_119" sap:VirtualizedContainerService.HintSize="832.666666666667,696" />
      <sap2010:ViewStateData Id="Sequence_143" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_67" sap:VirtualizedContainerService.HintSize="490,402" />
      <sap2010:ViewStateData Id="Sequence_84" sap:VirtualizedContainerService.HintSize="512,628">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_66" sap:VirtualizedContainerService.HintSize="638,782" />
      <sap2010:ViewStateData Id="Sequence_80" sap:VirtualizedContainerService.HintSize="660,1296">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_63" sap:VirtualizedContainerService.HintSize="950,1450" />
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="972,1676">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_62" sap:VirtualizedContainerService.HintSize="1098,1830" />
      <sap2010:ViewStateData Id="Sequence_78" sap:VirtualizedContainerService.HintSize="242,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_83" sap:VirtualizedContainerService.HintSize="264,885.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_65" sap:VirtualizedContainerService.HintSize="554,1037.33333333333" />
      <sap2010:ViewStateData Id="Sequence_82" sap:VirtualizedContainerService.HintSize="576,1262.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_64" sap:VirtualizedContainerService.HintSize="3992.66666666667,1414.66666666667" />
      <sap2010:ViewStateData Id="Sequence_81" sap:VirtualizedContainerService.HintSize="4014.66666666667,2272">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_61" sap:VirtualizedContainerService.HintSize="4304.66666666667,2424" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="4326.66666666667,2649.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_60" sap:VirtualizedContainerService.HintSize="4452.66666666667,2801.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_76" sap:VirtualizedContainerService.HintSize="4474.66666666667,3049.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_335" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_36" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_150" sap:VirtualizedContainerService.HintSize="264,247.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_59" sap:VirtualizedContainerService.HintSize="4764.66666666667,3201.33333333333" />
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="4787,3902">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_58" sap:VirtualizedContainerService.HintSize="4912,4050" />
      <sap2010:ViewStateData Id="Sequence_12" sap:VirtualizedContainerService.HintSize="4934,4236">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="5061,4384">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="712,22" />
      <sap2010:ViewStateData Id="Assign_57" sap:VirtualizedContainerService.HintSize="712,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_58" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="464,402" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="486,588">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="712,742" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="734,1030">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="486,440">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="1246,1184">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_59" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="1246,216" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="1268,1564">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="5060.66666666667,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_10" sap:VirtualizedContainerService.HintSize="5083,5334">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_20" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="5372,5482" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="5394,7821">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="5416,9017">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_49" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="264,348.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="5705,9165" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="5727,9389">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="200,52">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="5952,9537">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="5974,9723">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="242,61.3333333333333" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="264,286.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="6263,9871" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="6285,11811">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="6325,11931" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>