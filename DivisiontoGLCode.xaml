﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/MANDALA_DEM/"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="businessRule" Type="InArgument(x:String)" />
    <x:Property Name="Division" Type="InArgument(x:String)" />
    <x:Property Name="AIT1" Type="OutArgument(x:String)" />
    <x:Property Name="AIT2" Type="OutArgument(x:String)" />
    <x:Property Name="AIT3" Type="OutArgument(x:String)" />
    <x:Property Name="AIT4" Type="OutArgument(x:String)" />
    <x:Property Name="AIT5" Type="OutArgument(x:String)" />
    <x:Property Name="AIT6" Type="OutArgument(x:String)" />
    <x:Property Name="AIT7" Type="OutArgument(x:String)" />
    <x:Property Name="vendorID" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="comb" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="brFomat" />
      <Variable x:TypeArguments="x:String" Name="businessRulesAPIURL" />
      <Variable x:TypeArguments="njl:JToken" Name="brDesOpt" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respOutput" />
      <Variable x:TypeArguments="x:Int32" Name="brRespStatus" />
      <Variable x:TypeArguments="x:Int32" Name="resp" />
    </Sequence.Variables>
    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
      <Assign sap2010:WorkflowViewState.IdRef="Assign_28">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_29">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_30">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_31">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_32">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_33">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
      <Assign sap2010:WorkflowViewState.IdRef="Assign_34">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">
            <Literal x:TypeArguments="x:String" Value="" />
          </InArgument>
        </Assign.Value>
      </Assign>
    </Sequence>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_37">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[""]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{ 'parameters': {'Division':'{{%division%}}' ,'VendorID': '{{%VendorID%}}'} }" Text="[brFomat]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>division</x:String>
            <x:String>VendorID</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Division</x:String>
            <x:String>vendorID</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[businessRulesAPIURL]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[TenantID +"IONSERVICES/businessrules/decision/execute/"+businessRule]</InArgument>
      </Assign.Value>
    </Assign>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[TenantID +&quot;IONSERVICES/businessrules/decision/execute/&quot;+businessRule]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>VendorID</x:String>
            <x:String>Division</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>vendorID</x:String>
            <x:String>division</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[brRespStatus = 200]" sap2010:WorkflowViewState.IdRef="If_2">
      <If.Then>
        <If Condition="[respOutput.ReadAsJson(&quot;parameters&quot;).HasValues]" sap2010:WorkflowViewState.IdRef="If_1">
          <If.Then>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                <TryCatch.Try>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_38">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("AIT1").toString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_39">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[AIT1]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
                <TryCatch.Try>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_40">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("AIT2").toString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_41">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[AIT2]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_7">
                <TryCatch.Try>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_50">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("AIT3").toString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_7">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_51">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[AIT3]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_6">
                <TryCatch.Try>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_48">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("AIT4").toString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_49">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[AIT4]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_5">
                <TryCatch.Try>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_46">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("AIT5").toString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_47">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[AIT5]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_4">
                <TryCatch.Try>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_44">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("AIT6").toString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_45">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[AIT6]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
              <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_3">
                <TryCatch.Try>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_42">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[respOutput.ReadAsJson("parameters")("AIT7").toString]</InArgument>
                    </Assign.Value>
                  </Assign>
                </TryCatch.Try>
                <TryCatch.Catches>
                  <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_3">
                    <ActivityAction x:TypeArguments="s:Exception">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                      </ActivityAction.Argument>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_43">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[AIT7]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                    </ActivityAction>
                  </Catch>
                </TryCatch.Catches>
              </TryCatch>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_35">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[comb]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[AIT1+"~"+AIT2+"~"+AIT3+"~"+AIT4+"~"+AIT5+"~"+AIT6+"~"+AIT7]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </If.Then>
        </If>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>d1xDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXERpdmlzaW9udG9HTENvZGUueGFtbGIBbgGlAQECTAPAAw4CAQFVBZwBEAIBbp0BBaQBDgIBaqUBBbIBGgIBZrMBBboBDgIBYLsBBbsBzgECAVu8AQXTAR8CAVLUAQW+AwoCAQJWB18QAwGHAWAHaRADAYMBagdzEAIBf3QHfRACAXt+B4cBEAIBd4gBB5EBEAIBc5IBB5sBEAIBb6IBMKIBNAIBbZ8BMZ8BNwIBa6UB+gGlAYUCAgFopQGjAaUB9AECAWe4ATC4AXYCAWO1ATG1AUYCAWG7AcABuwHLAQIBXrsBpgG7AbIBAgFcvAH3AbwBjgICAVq8AZgCvAGmAgIBWLwBxwK8AZkDAgFVvAGyArwBwgICAVPUARPUASkCAQPWAQm8Aw4CAQVcDVw8AwGKAVgzWDkDAYgBZg1mPAMBhgFiM2I5AwGEAXANcDwDAYIBbDNsOQMBgAF6DXo8AgF+djN2OQIBfIQBDYQBPAIBeoABM4ABOQIBeI4BDY4BPAIBdooBM4oBOQIBdJgBDZgBPAIBcpQBM5QBOQIBcNYBF9YBUgIBBtgBDboDGAIBB9kBD/cBGgIBSfgBD5YCGgIBQJcCD7UCGgIBN7YCD9QCGgIBLtUCD/MCGgIBJfQCD5IDGgIBHJMDD7EDGgIBE7IDD7kDGAIBCNsBE+IBHAIBTuoBF/MBIAIBSvoBE4ECHAIBRYkCF5ICIAIBQZkCE6ACHAIBPKgCF7ECIAIBOLgCE78CHAIBM8cCF9ACIAIBL9cCE94CHAIBKuYCF+8CIAIBJvYCE/0CHAIBIYUDF44DIAIBHZUDE5wDHAIBGKQDF60DIAIBFLcDOrcDdgIBC7QDO7QDQQIBCeABPuABdAIBUd0BP90BRQIBT/ABHfABTAIBTewBQ+wBSQIBS/8BPv8BdAIBSPwBP/wBRQIBRo8CHY8CTAIBRIsCQ4sCSQIBQp4CPp4CdAIBP5sCP5sCRQIBPa4CHa4CTAIBO6oCQ6oCSQIBOb0CPr0CdAIBNroCP7oCRQIBNM0CHc0CTAIBMskCQ8kCSQIBMNwCPtwCdAIBLdkCP9kCRQIBK+wCHewCTAIBKegCQ+gCSQIBJ/sCPvsCdAIBJPgCP/gCRQIBIosDHYsDTAIBIIcDQ4cDSQIBHpoDPpoDdAIBG5cDP5cDRQIBGaoDHaoDTAIBF6YDQ6YDSQIBFQ==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="589,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_37" sap:VirtualizedContainerService.HintSize="589,60" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="589,22" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="589,60" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="589,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="589,22" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="400,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_40" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_41" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="400,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_50" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_51" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_7" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_49" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="400,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_6" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_46" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_47" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="400,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_44" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="400,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_42" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_43" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_3" sap:VirtualizedContainerService.HintSize="400,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_3" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,821">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,969" />
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="589,1117" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="611,1718">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="651,1798" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>