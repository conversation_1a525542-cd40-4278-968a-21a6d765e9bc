﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="vat" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="CountryCode" Type="InArgument(x:String)" />
    <x:Property Name="VatPercentage" Type="InArgument(x:String)" />
    <x:Property Name="invoiceType" Type="InArgument(x:String)" />
    <x:Property Name="AccountStartsWith" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="VatConfiguration_Sequence_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="vatCode" />
      <Variable x:TypeArguments="njl:JToken" Name="brDesOpt" />
      <Variable x:TypeArguments="x:String" Name="brFomat" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respOutput" />
      <Variable x:TypeArguments="x:Int32" Name="brRespStatus" />
      <Variable x:TypeArguments="x:Int32" Name="respout" />
      <Variable x:TypeArguments="njl:JToken" Name="out5" />
      <Variable x:TypeArguments="x:String" Name="vatAmt" />
      <Variable x:TypeArguments="x:Int32" Name="totalVats" />
      <Variable x:TypeArguments="x:Int32" Default="0" Name="Itr" />
      <Variable x:TypeArguments="x:String" Name="percentage" />
      <Variable x:TypeArguments="x:String" Name="per" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
    </Sequence.Variables>
    <Assign DisplayName="VatConfiguration_Assign_AccountStartsWith_2" sap2010:WorkflowViewState.IdRef="Assign_24">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[AccountStartsWith]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[If(invoiceType = "po","",AccountStartsWith)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="VatConfiguration_Assign_percentage_3" sap2010:WorkflowViewState.IdRef="Assign_18">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[percentage]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[VatPercentage]</InArgument>
      </Assign.Value>
    </Assign>
    <TryCatch DisplayName="VatConfiguration_TryCatch_totalVats_4" sap2010:WorkflowViewState.IdRef="TryCatch_5">
      <TryCatch.Try>
        <Assign DisplayName="VatConfiguration_Assign_totalVats_5" sap2010:WorkflowViewState.IdRef="Assign_15">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Int32">[totalVats]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Int32">[vat.split(","c).length]</InArgument>
          </Assign.Value>
        </Assign>
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
            <Assign DisplayName="VatConfiguration_Assign_totalVatsException_6" sap2010:WorkflowViewState.IdRef="Assign_32">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[totalVats]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[1]</InArgument>
              </Assign.Value>
            </Assign>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <If Condition="[totalVats&gt;1]" DisplayName="VatConfiguration_If_totalVats_7" sap2010:WorkflowViewState.IdRef="If_13">
      <If.Then>
        <While DisplayName="VatConfiguration_While_Itr_8" sap2010:WorkflowViewState.IdRef="While_1" Condition="[Itr &lt; totalVats]">
          <Sequence DisplayName="VatConfiguration_Sequence_WhileSequence_9" sap2010:WorkflowViewState.IdRef="Sequence_8">
            <Assign DisplayName="VatConfiguration_Assign_vatAmt_10" sap2010:WorkflowViewState.IdRef="Assign_16">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[vatAmt]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[vat.split(","c)(Itr)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign DisplayName="VatConfiguration_Assign_per_11" sap2010:WorkflowViewState.IdRef="Assign_19">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[per]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[percentage.split(","c)(Itr).Replace("%","")]</InArgument>
              </Assign.Value>
            </Assign>
            <TryCatch DisplayName="VatConfiguration_TryCatch_VatPercentage_12" sap2010:WorkflowViewState.IdRef="TryCatch_2">
              <TryCatch.Try>
                <Assign DisplayName="VatConfiguration_Assign_VatPercentageConvert_13" sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Convert.toDouble(Regex.Replace(per, "%", "").Trim()).tostring]</InArgument>
                  </Assign.Value>
                </Assign>
              </TryCatch.Try>
              <TryCatch.Catches>
                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                  <ActivityAction x:TypeArguments="s:Exception">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                    </ActivityAction.Argument>
                    <Assign DisplayName="VatConfiguration_Assign_VatPercentageZero_14" sap2010:WorkflowViewState.IdRef="Assign_25">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">0</InArgument>
                      </Assign.Value>
                    </Assign>
                  </ActivityAction>
                </Catch>
              </TryCatch.Catches>
            </TryCatch>
            <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_TemplateApply_brfomat_15" sap2010:WorkflowViewState.IdRef="Template_Apply_5" Template="{}{ 'parameters': {  'AccountStartsWith':'{{%AccountStartsWith%}}','VatPercentage': '{{%VatPercentage%}}' ,'CountryCode': '{{%CountryCode%}}','invoiceType': '{{%invoiceType%}}' } }" Text="[brfomat]">
              <ias:Template_Apply.Values>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>VatPercentage</x:String>
                    <x:String>CountryCode</x:String>
                    <x:String>invoiceType</x:String>
                    <x:String>AccountStartsWith</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>VatPercentage</x:String>
                    <x:String>CountryCode</x:String>
                    <x:String>invoiceType</x:String>
                    <x:String>AccountStartsWith</x:String>
                  </scg:List>
                </scg:List>
              </ias:Template_Apply.Values>
            </ias:Template_Apply>
            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_DeserializeJSON_brDesOpt_16" sap2010:WorkflowViewState.IdRef="DeserializeJSON_4" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respOutput_19" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[tenantID+&quot;IONSERVICES/businessrules/decision/execute/&quot;+&quot;VatCodeConfiguration&quot;]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
              <iai:IONAPIRequestWizard.QueryParameters>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>matrixName</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>VatCodeConfiguration</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.QueryParameters>
            </iai:IONAPIRequestWizard>
            <Assign DisplayName="VatConfiguration_Assign_vatCode_22" sap2010:WorkflowViewState.IdRef="Assign_21">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("parameters")("VatCode").ToString]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[vatCode = &quot;NA&quot;]" DisplayName="VatConfiguration_If_vatCodeNA_23" sap2010:WorkflowViewState.IdRef="If_12">
              <If.Then>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respout1_24" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>INBN</x:String>
                        <x:String>RDTP</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>GLAM</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>inbnValue</x:String>
                        <x:String>3</x:String>
                        <x:String>division</x:String>
                        <x:String>vatAmt</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </If.Then>
              <If.Else>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respout1Else_25" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>INBN</x:String>
                        <x:String>RDTP</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>VTA1</x:String>
                        <x:String>VTCD</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>inbnValue</x:String>
                        <x:String>3</x:String>
                        <x:String>division</x:String>
                        <x:String>vatAmt</x:String>
                        <x:String>vatCode</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </If.Else>
            </If>
            <Assign DisplayName="VatConfiguration_Assign_out5_26" sap2010:WorkflowViewState.IdRef="Assign_12">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respout1.ReadAsText)]</InArgument>
              </Assign.Value>
            </Assign>
            <TryCatch DisplayName="VatConfiguration_TryCatch_ResponseCheck_27" sap2010:WorkflowViewState.IdRef="TryCatch_1">
              <TryCatch.Try>
                <If Condition="[respout = 200]" DisplayName="VatConfiguration_If_respout200_28" sap2010:WorkflowViewState.IdRef="If_7">
                  <If.Then>
                    <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="VatConfiguration_If_out5Records_29" sap2010:WorkflowViewState.IdRef="If_6">
                      <If.Then>
                        <Sequence DisplayName="VatConfiguration_Sequence_ErrorSequence_30" sap2010:WorkflowViewState.IdRef="Sequence_6">
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_errorMessage_31" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_VatLineAdded_32" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="Vat line added" Source="[logfile]" />
                      </If.Else>
                    </If>
                  </If.Then>
                  <If.Else>
                    <Sequence DisplayName="VatConfiguration_Sequence_ErrorSequence2_33" sap2010:WorkflowViewState.IdRef="Sequence_7">
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_ErrorAddingVat_34" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="Error while adding the Vat line" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </TryCatch.Try>
              <TryCatch.Catches>
                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                  <ActivityAction x:TypeArguments="s:Exception">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                    </ActivityAction.Argument>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_ExceptionVat_35" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="Exception while adding Vat line" Source="[logfile]" />
                  </ActivityAction>
                </Catch>
              </TryCatch.Catches>
            </TryCatch>
            <Assign DisplayName="VatConfiguration_Assign_ItrIncrement_36" sap2010:WorkflowViewState.IdRef="Assign_17">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[Itr]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[Itr+1]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </While>
      </If.Then>
      <If.Else>
        <Sequence DisplayName="VatConfiguration_Sequence_ElseSequence_37" sap2010:WorkflowViewState.IdRef="Sequence_11">
          <TryCatch DisplayName="VatConfiguration_TryCatch_VatPercentageElse_38" sap2010:WorkflowViewState.IdRef="TryCatch_6">
            <TryCatch.Try>
              <Assign DisplayName="VatConfiguration_Assign_VatPercentageConvertElse_39" sap2010:WorkflowViewState.IdRef="Assign_35">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[Convert.toDouble(Regex.Replace(VatPercentage, "%", "").Trim()).tostring]</InArgument>
                </Assign.Value>
              </Assign>
            </TryCatch.Try>
            <TryCatch.Catches>
              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                <ActivityAction x:TypeArguments="s:Exception">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                  </ActivityAction.Argument>
                  <Assign DisplayName="VatConfiguration_Assign_VatPercentageZeroElse_40" sap2010:WorkflowViewState.IdRef="Assign_36">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">0</InArgument>
                    </Assign.Value>
                  </Assign>
                </ActivityAction>
              </Catch>
            </TryCatch.Catches>
          </TryCatch>
          <Assign DisplayName="VatConfiguration_Assign_vatAmtElse_41" sap2010:WorkflowViewState.IdRef="Assign_26">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vatAmt]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[vat]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_TemplateApply_brfomatElse_42" sap2010:WorkflowViewState.IdRef="Template_Apply_6" Template="{}{ 'parameters': {  'AccountStartsWith':'{{%AccountStartsWith%}}','VatPercentage': '{{%VatPercentage%}}' ,'CountryCode': '{{%CountryCode%}}','invoiceType': '{{%invoiceType%}}' } }" Text="[brfomat]">
            <ias:Template_Apply.Values>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VatPercentage</x:String>
                  <x:String>CountryCode</x:String>
                  <x:String>invoiceType</x:String>
                  <x:String>AccountStartsWith</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VatPercentage</x:String>
                  <x:String>CountryCode</x:String>
                  <x:String>invoiceType</x:String>
                  <x:String>AccountStartsWith</x:String>
                </scg:List>
              </scg:List>
            </ias:Template_Apply.Values>
          </ias:Template_Apply>
          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_DeserializeJSON_brDesOptElse_43" sap2010:WorkflowViewState.IdRef="DeserializeJSON_5" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respOutputElse_46" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[tenantID+&quot;IONSERVICES/businessrules/decision/execute/&quot;+&quot;VatCodeConfiguration&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="0" />
                <scg:List x:TypeArguments="x:String" Capacity="0" />
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>matrixName</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VatCodeConfiguration</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <Assign DisplayName="VatConfiguration_Assign_vatCodeElse_49" sap2010:WorkflowViewState.IdRef="Assign_30">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("parameters")("VatCode").ToString]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[vatCode = &quot;NA&quot;]" DisplayName="VatConfiguration_If_vatCodeNAElse_50" sap2010:WorkflowViewState.IdRef="If_14">
            <If.Then>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respout1ElseThen_51" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>INBN</x:String>
                      <x:String>RDTP</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>GLAM</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>inbnValue</x:String>
                      <x:String>3</x:String>
                      <x:String>division</x:String>
                      <x:String>vatAmt</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </If.Then>
            <If.Else>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respout1ElseElse_52" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>INBN</x:String>
                      <x:String>RDTP</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>VTA1</x:String>
                      <x:String>VTCD</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>inbnValue</x:String>
                      <x:String>3</x:String>
                      <x:String>division</x:String>
                      <x:String>vatAmt</x:String>
                      <x:String>vatCode</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </If.Else>
          </If>
          <Assign DisplayName="VatConfiguration_Assign_out5Else_53" sap2010:WorkflowViewState.IdRef="Assign_31">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respout1.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <TryCatch DisplayName="VatConfiguration_TryCatch_ResponseCheckElse_54" sap2010:WorkflowViewState.IdRef="TryCatch_4">
            <TryCatch.Try>
              <If Condition="[respout = 200]" DisplayName="VatConfiguration_If_respout200Else_55" sap2010:WorkflowViewState.IdRef="If_16">
                <If.Then>
                  <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="VatConfiguration_If_out5RecordsElse_56" sap2010:WorkflowViewState.IdRef="If_15">
                    <If.Then>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_errorMessageElse_57" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                    </If.Then>
                    <If.Else>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_VatLineAddedElse_58" sap2010:WorkflowViewState.IdRef="Append_Line_9" Line="Vat line added" Source="[logfile]" />
                    </If.Else>
                  </If>
                </If.Then>
                <If.Else>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_ErrorAddingVatElse_59" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="Error while adding the Vat line" Source="[logfile]" />
                </If.Else>
              </If>
            </TryCatch.Try>
            <TryCatch.Catches>
              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                <ActivityAction x:TypeArguments="s:Exception">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                  </ActivityAction.Argument>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_ExceptionVatElse_60" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="Exception while adding Vat line" Source="[logfile]" />
                </ActivityAction>
              </Catch>
            </TryCatch.Catches>
          </TryCatch>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d1xDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXHZhdENvbmZpZ3VyYXRpb24ueGFtbIkBTAOYBA4CAQFXM1c2AgECXAVjDgMBxQFkBWsOAwHAAWwFiAEQAwG2AYkBBZYECgIBA2EwYV0DAcgBXjFeRAMBxgFpMGk/AwHDAWYxZj0DAcEBbgl1EgMBuwF9DYQBFgMBtwGJAROJASUCAQSLAQnZAhECAVXcAgmUBBQCAQZzM3NLAwG+AXA0cD8DAbwBggE3ggE6AwG6AX84f0MDAbgBjAEL2AIWAgFZiwFviwGFAQIBVt0CC/kCFgIBTPoCC4EDFAIBR4IDC5MDIAIBQ5QDC5QD9AECAT6VAwumAyUCATanAwuuAxQCATKvAwvuAxACASHvAwv2AxQCAR33AwuTBBYCAQeNAQ2UARYDAbABlQENnAEWAwGqAZ0BDbkBGAMBoQG6AQ3LASIDAZ0BzAENzAHyAQMBmAHNAQ3eAScDAZAB3wEN5gEWAwGMAecBDaYCEgIBe6cCDa4CFgIBd68CDc8CGAIBX9ACDdcCFgIBWt8CD+YCGAIBUe4CE/UCHAIBTf8CNv8COwIBSvwCN/wCPwIBSIID/wKCA4oDAgFFggPDAYID+QICAUSUA+YBlAPxAQIBQZQDzAGUA9gBAgE/lQOlApUDvAICAT2VA8YClQPUAgIBO5UD9QKVA9oDAgE5lQPgApUD8AICATesAzasA30CATWpAzepA0ACATOvAxmvAzUCASKxAw/MAykCASvPAw/sAykCAST0Azj0A1sCASDxAznxAz8CAR75Aw+HBBQCAQyPBBOPBPsBAgEIkgE4kgFOAwGzAY8BOY8BQQMBsQGaATiaAWUDAa0BlwE5lwE+AwGrAZ8BEaYBGgMBpgGuARW1AR4DAaIBugH9AroBiAMDAZ8BugHBAboB9wIDAZ4BzAHkAcwB7wEDAZsBzAHKAcwB1gEDAZkBzQGjAs0BugIDAZcBzQHEAs0B0gIDAZUBzQHzAs0B2AMDAZMBzQHeAs0B7gIDAZEB5AE45AF/AwGPAeEBOeEBQgMBjQHnARvnATcCAXzpARGEAisDAYUBhwIRpAIrAgF+rAI6rAJdAgF6qQI7qQJBAgF4sQIRwwIWAgFkywIVywL4AQIBYNUCN9UCPgIBXdICONICPQIBW+QCOuQCgwECAVThAjvhAkoCAVLzAj7zAj8CAVDwAj/wAk4CAU6xA9cCsQPiAgIBMLEDvQKxA8kCAgEusQPnArEDqgMCASzPA9cCzwPiAgIBKc8DvQLPA8kCAgEnzwPnAs8DqgMCASX5Ax35Ay4CAQ37AxOCBBgCAROFBBOFBP0BAgEPjwTEAY8E5QECAQuPBO0BjwT4AQIBCaQBPKQBewMBqQGhAT2hAUwDAacBswFAswFBAwGlAbABQbABUAMBowHpAdAC6QHbAgMBigHpAbYC6QHCAgMBiAHpAeAC6QGjAwMBhgGHAtQChwLfAgMBgwGHAroChwLGAgMBgQGHAuQChwKnAwIBf7ECH7ECMAIBZbMCFbwCGgIBbL8CFcECIAIBZ8sCwQHLAuIBAgFjywLqAcsC9QECAWH7AyH7A3ACART9Axf9A6MCAgEZgAQXgATtAQIBFYUExgGFBOcBAgEShQTvAYUE+gECARCzAiOzAnICAW21Ahm3AiQCAXK6Ahm6AusBAgFuwAIXwAL8AQIBaP0DyAH9A40CAgEc/QOVAv0DoAICARqABMcBgATXAQIBGIAE3wGABOoBAgEWtgIbtgKiAgIBc7oCxQG6AtUBAgFxugLdAboC6AECAW/AAsUBwALmAQIBa8AC7gHAAvkBAgFptgLHAbYCjAICAXa2ApQCtgKfAgIBdA==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="1266,60" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1266,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="1266,287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="464,287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_4" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="464,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="464,294" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="711,442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="716.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="464,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="486,1436">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="512,1594">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_6" sap:VirtualizedContainerService.HintSize="707,287">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="707,60" />
      <sap2010:ViewStateData Id="Template_Apply_6" sap:VirtualizedContainerService.HintSize="707,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_5" sap:VirtualizedContainerService.HintSize="707,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="707,22" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="707,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="707,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="707,60" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="689,356">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="693,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="707,583">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="729,1768">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="1266,1916" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="1288,2567">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1328,2647" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>