﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iad1="clr-namespace:Infor.Activities.Desktop;assembly=Infor.Activities.Desktop"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Web;assembly=Infor.Activities.Web"
 xmlns:iaw1="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iro="clr-namespace:Infor.RPA.OCR;assembly=Infor.RPA.OCR"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="promptPath" Type="InArgument(x:String)" />
    <x:Property Name="promptPath2" Type="InArgument(x:String)" />
    <x:Property Name="copiedFilePath" Type="InArgument(x:String)" />
    <x:Property Name="InProgressFolder" Type="InArgument(x:String)" />
    <x:Property Name="MasterDownloads" Type="InArgument(x:String)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="strSplitExtension" Type="InArgument(x:String)" />
    <x:Property Name="strClassificationExtension" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Linq.Expressions</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.IO</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="Classification and Split Sequence" sap2010:WorkflowViewState.IdRef="Sequence_79">
    <Sequence.Variables>
      <Variable x:TypeArguments="scg:List(x:String)" Name="MasterDownloadedFiles" />
      <Variable x:TypeArguments="njl:JToken" Name="jout" />
      <Variable x:TypeArguments="njl:JToken" Name="values" />
      <Variable x:TypeArguments="x:Int32" Name="genAIRespCode" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:String)" Name="invoicePageDictionary" />
      <Variable x:TypeArguments="x:String" Name="noOfPages" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="ListOCRData" />
      <Variable x:TypeArguments="scg:List(x:String)" Name="ListClassificationData" />
      <Variable x:TypeArguments="x:String" Name="CurrentOCRFile" />
      <Variable x:TypeArguments="x:String" Name="CurrentClassificationFIle" />
      <Variable x:TypeArguments="x:String" Name="ClassificationText" />
      <Variable x:TypeArguments="x:String" Name="OCRText" />
      <Variable x:TypeArguments="x:String" Name="FinalPages" />
      <Variable x:TypeArguments="x:String" Name="NameForInvoice" />
      <Variable x:TypeArguments="x:Boolean" Name="blnInvDictExists" />
      <Variable x:TypeArguments="x:String" Name="strCurVendorSupportingPages" />
      <Variable x:TypeArguments="x:String" Name="CurInvPageNoAll" />
      <Variable x:TypeArguments="x:Int32" Default="1" Name="UnstructuredCounter" />
      <Variable x:TypeArguments="x:String" Name="CurInvoiceNumber" />
      <Variable x:TypeArguments="x:String" Name="strVendName" />
      <Variable x:TypeArguments="x:String" Name="GenAIModelVersion" />
      <Variable x:TypeArguments="x:String" Name="GenAIModel" />
      <Variable x:TypeArguments="x:String" Name="pdfFileName" />
      <Variable x:TypeArguments="x:String" Name="outputStructure" />
      <Variable x:TypeArguments="x:String" Name="value" />
      <Variable x:TypeArguments="x:String" Name="customTextSplit" />
      <Variable x:TypeArguments="x:Boolean" Name="blncustomExists1" />
    </Sequence.Variables>
    <Sequence DisplayName="OCR Extraction &amp; Classification Sequence" sap2010:WorkflowViewState.IdRef="Sequence_108">
      <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[MasterDownloads]" DisplayName="Get Files in OutlookDownloads" FileType="PDF" Files="[MasterDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_1" IncludeSubDir="True" />
      <ForEach x:TypeArguments="x:String" DisplayName="OCR Extraction and Classification for all Files" sap2010:WorkflowViewState.IdRef="ForEach`1_33" Values="[MasterDownloadedFiles]">
        <ActivityAction x:TypeArguments="x:String">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="x:String" Name="documentPath" />
          </ActivityAction.Argument>
          <Sequence DisplayName="OCR Extraction and Classification for all Files Sequence" sap2010:WorkflowViewState.IdRef="Sequence_51">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="promptText" />
              <Variable x:TypeArguments="x:Int32" Name="responseCode" />
              <Variable x:TypeArguments="x:String" Name="OCRtxtFile" />
              <Variable x:TypeArguments="x:String" Name="ClassifcationtxtFile" />
            </Sequence.Variables>
            <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_4" Source="[promptPath]" Text="[promptText]" />
            <iro:DocumentOC Pages="{x:Null}" ActivityID="f69e5a30-0b3c-4f29-9d8a-1e6947a44cd8" ContinueOnError="False" DisplayName="Get OCR Text" ErrorCode="[responseCode]" ExecuteFile="True" FilePath="[documentPath]" sap2010:WorkflowViewState.IdRef="DocumentOC_2" ResponseObject="[values]" />
            <ias:File_Create ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Create OCR Output File" sap2010:WorkflowViewState.IdRef="File_Create_3" Name="[Path.GetFileName(documentPath).Substring(0, Path.GetFileName(documentPath).Length - 4)+&quot;.txt&quot;]" OutputFile="[OCRtxtFile]" Target="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" />
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append OCR Data" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[values.ToString]" Source="[OCRtxtFile]" />
            <Switch x:TypeArguments="x:Boolean" DisplayName="Check Get OCR Status" Expression="[responseCode = 200]" sap2010:WorkflowViewState.IdRef="Switch`1_3">
              <Sequence x:Key="True" DisplayName="Process OCR Output" sap2010:WorkflowViewState.IdRef="Sequence_50">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="promptRequest" />
                  <Variable x:TypeArguments="x:String" Name="IonBody" />
                  <Variable x:TypeArguments="njl:JToken" Name="genAIRequestToken" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse" />
                  <Variable x:TypeArguments="njl:JToken" Name="out1" />
                  <Variable x:TypeArguments="x:String" Name="out2" />
                  <Variable x:TypeArguments="scg:List(x:String)" Name="txtFiles" />
                  <Variable x:TypeArguments="x:Boolean" Name="customExists" />
                  <Variable x:TypeArguments="x:String" Name="customText" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_75">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[noOfPages]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[values("_metadata")("NumberOfPages").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_76">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[customText]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">
                      <Literal x:TypeArguments="x:String" Value="" />
                    </InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_2" IsValid="[customExists]" Path="[strClassificationExtension]" />
                <If Condition="[customExists]" DisplayName="Check if Custom Prompt Exists" sap2010:WorkflowViewState.IdRef="If_23">
                  <If.Then>
                    <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_5" Source="[strClassificationExtension]" Text="[customText]" />
                  </If.Then>
                </If>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_468">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[miscValues("GenAIModel").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_469">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[miscValues("GenAIModelVersion").ToString]</InArgument>
                  </Assign.Value>
                </Assign>
                <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_22">
                  <iad:CommentOut.Activities>
                    <Sequence DisplayName="Prompt API Sequence" sap2010:WorkflowViewState.IdRef="Sequence_148">
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_77">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[promptText.Replace("{text}",values.tostring)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_78">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[promptRequest.Replace("{notes}",customText.tostring)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_79">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_13" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':4096,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}'&#xA;" Text="[IonBody]">
                        <ias:Template_Apply.Values>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>prompt</x:String>
                              <x:String>model</x:String>
                              <x:String>version</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="4">
                              <x:String>promptRequest</x:String>
                              <x:String>GenAIModel</x:String>
                              <x:String>GenAIModelVersion</x:String>
                            </scg:List>
                          </scg:List>
                        </ias:Template_Apply.Values>
                      </ias:Template_Apply>
                    </Sequence>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_80">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[ocrOutput]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[values.toString.Replace("'","")]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </iad:CommentOut.Activities>
                </iad:CommentOut>
                <Sequence DisplayName="Messages API Sequence - No Image" sap2010:WorkflowViewState.IdRef="Sequence_149">
                  <Assign DisplayName="1Assign" sap2010:WorkflowViewState.IdRef="Assign_480">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[promptText.Replace("{notes}",customText.tostring)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="2Assign" sap2010:WorkflowViewState.IdRef="Assign_481">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[promptRequest.Replace("'","\'")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="3Assign" sap2010:WorkflowViewState.IdRef="Assign_482">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="4Assign" sap2010:WorkflowViewState.IdRef="Assign_483">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[promptRequest]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[promptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="5Assign" sap2010:WorkflowViewState.IdRef="Assign_484">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[Regex.Replace(Values.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_25">
                    <iad:CommentOut.Activities>
                      <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_14" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the questions using the ocr text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                        <ias:Template_Apply.Values>
                          <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                            <scg:List x:TypeArguments="x:String" Capacity="8">
                              <x:String>prompt</x:String>
                              <x:String>model</x:String>
                              <x:String>version</x:String>
                              <x:String>ocrText</x:String>
                              <x:String>outputStructure</x:String>
                            </scg:List>
                            <scg:List x:TypeArguments="x:String" Capacity="8">
                              <x:String>promptRequest</x:String>
                              <x:String>GenAIModel</x:String>
                              <x:String>GenAIModelVersion</x:String>
                              <x:String>value</x:String>
                              <x:String>outputStructure</x:String>
                            </scg:List>
                          </scg:List>
                        </ias:Template_Apply.Values>
                      </ias:Template_Apply>
                    </iad:CommentOut.Activities>
                  </iad:CommentOut>
                  <ias:Template_Apply ErrorCode="{x:Null}" sap2010:Annotation.AnnotationText="Updated Templating" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_16" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are expert document based question answering tool. You will be provided with the raw text of the document using textract. You are to identify the page numbers for a specific vendor where and all there are invoice related pages available. You will also be provided with some instructions on how to identify the correct page numbers for a given vendor from the document. Make sure to understand the provided example to correctly arrive at the invoice and supporting page numbers from the raw text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody]">
                    <ias:Template_Apply.Values>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="8">
                          <x:String>prompt</x:String>
                          <x:String>model</x:String>
                          <x:String>version</x:String>
                          <x:String>ocrText</x:String>
                          <x:String>outputStructure</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="8">
                          <x:String>promptRequest</x:String>
                          <x:String>GenAIModel</x:String>
                          <x:String>GenAIModelVersion</x:String>
                          <x:String>value</x:String>
                          <x:String>outputStructure</x:String>
                        </scg:List>
                      </scg:List>
                    </ias:Template_Apply.Values>
                  </ias:Template_Apply>
                </Sequence>
                <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_9" JTokenObject="[genAIRequestToken]" JTokenString="[IonBody]" />
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" PostData="[genAIRequestToken.tostring]" Response="[genAIResponse]" ResponseCode="[genAIRespCode]" Url="[tenantID + &quot;genai/chatsvc/api/v1/messages&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>x-infor-logicalidprefix</x:String>
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>lid://infor.colemanddp</x:String>
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                      <scg:List x:TypeArguments="x:String" Capacity="0" />
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
                  <Assign.To>
                    <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse.ReadAsText)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_83">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("content").ToString.Replace("\n","")]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_84">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[out2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[(out2.ToString.Replace("None","''")).Replace("''''","''")]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_10" JTokenObject="[jout]" JTokenString="[out2.ToString]" />
                <ias:File_Create ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Create ClassificationData Output File" sap2010:WorkflowViewState.IdRef="File_Create_4" Name="[Path.GetFileName(documentPath).Substring(0, Path.GetFileName(documentPath).Length - 4)+&quot;.txt&quot;]" OutputFile="[ClassifcationtxtFile]" Target="[configurationFolder+&quot;\OutlookDownloads\ClassificationData&quot;]" />
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Append Classification Data" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[jout.ToString]" Source="[ClassifcationtxtFile]" />
              </Sequence>
            </Switch>
          </Sequence>
        </ActivityAction>
      </ForEach>
    </Sequence>
    <Switch x:TypeArguments="x:Boolean" DisplayName="Check if SplittingDoc is True" Expression="[miscValues(&quot;SplittingDoc&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="Switch`1_27">
      <Sequence x:Key="True" DisplayName="Split True Sequence" sap2010:WorkflowViewState.IdRef="Sequence_109">
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Get OCR Files in OCR Data" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_2" IncludeSubDir="True" />
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\ClassificationData&quot;]" DisplayName="Get Classification Data Files in ClassificationData" FileType="All" Files="[ListClassificationData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_3" IncludeSubDir="True" />
        <ForEach x:TypeArguments="x:String" DisplayName="ForEach PDF in MasterDownloads folder" sap2010:WorkflowViewState.IdRef="ForEach`1_40" Values="[MasterDownloadedFiles]">
          <ActivityAction x:TypeArguments="x:String">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="x:String" Name="item" />
            </ActivityAction.Argument>
            <Sequence DisplayName="Inside ForEach PDF in MasterDownloads folder Sequence" sap2010:WorkflowViewState.IdRef="Sequence_94">
              <Sequence.Variables>
                <Variable x:TypeArguments="s:String[]" Name="ArrayTargetPages" />
                <Variable x:TypeArguments="x:String" Name="strSpecificText" />
              </Sequence.Variables>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_308">
                <Assign.To>
                  <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoicePageDictionary]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[New Dictionary (Of String, String)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_231">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
  ListOCRData.
    Where(Function(f) Path.GetFileNameWithoutExtension(f).Equals(Path.GetFileNameWithoutExtension(item), StringComparison.OrdinalIgnoreCase)).
    FirstOrDefault(),
  "NA"
)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_232">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[CurrentClassificationFIle]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
  ListClassificationData.
    Where(Function(f) Path.GetFileNameWithoutExtension(f).Equals(Path.GetFileNameWithoutExtension(item), StringComparison.OrdinalIgnoreCase)).
    FirstOrDefault(),
  "NA"
)]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read OCR File" sap2010:WorkflowViewState.IdRef="File_Read_12" Source="[CurrentOCRFile]" Text="[OCRText]" />
              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read Classification File" sap2010:WorkflowViewState.IdRef="File_Read_13" Source="[CurrentClassificationFIle]" Text="[ClassificationText]" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_300">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[values]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRText)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_233">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[Jout]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ClassificationText)]</InArgument>
                </Assign.Value>
              </Assign>
              <ForEach x:TypeArguments="njl:JProperty" DisplayName="ForEach &lt;JProperty&gt; in Classification Output" sap2010:WorkflowViewState.IdRef="ForEach`1_46" Values="[(JObject.Parse(jout.ToString)).Properties()]">
                <ActivityAction x:TypeArguments="njl:JProperty">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="njl:JProperty" Name="Joutitem" />
                  </ActivityAction.Argument>
                  <Sequence DisplayName="Inside ForEach &lt;JProperty&gt; in Classification Output Sequence" sap2010:WorkflowViewState.IdRef="Sequence_107">
                    <Sequence.Variables>
                      <Variable x:TypeArguments="njl:JToken" Name="ReqOCRvalues" />
                      <Variable x:TypeArguments="x:String" Name="targetPages" />
                    </Sequence.Variables>
                    <Switch x:TypeArguments="x:String" DisplayName="Check Item Type" Expression="[Joutitem.Value(0).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_8">
                      <Sequence x:Key="Invoice" DisplayName="Invoice Sequence" sap2010:WorkflowViewState.IdRef="Sequence_99">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="Page" />
                          <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                          <Variable x:TypeArguments="x:String" Name="base64string" />
                          <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                          <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                          <Variable x:TypeArguments="x:String" Name="IonBody1" />
                          <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                          <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                          <Variable x:TypeArguments="njl:JToken" Name="out3" />
                          <Variable x:TypeArguments="x:String" Name="out4" />
                          <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_459">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strVendName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Joutitem.Name.Tostring]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Sequence DisplayName="Specific Vendor OCR Text" sap2010:WorkflowViewState.IdRef="Sequence_145">
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_462">
                            <Assign.To>
                              <OutArgument x:TypeArguments="njl:JToken">[ReqOCRvalues]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRText)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_463">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[targetPages]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[String.Join(",", CType(Joutitem.Value(1), JArray).Select(Function(x) x.ToString()))]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_464">
                            <Assign.To>
                              <OutArgument x:TypeArguments="s:String[]">[ArrayTargetPages]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="s:String[]">[targetPages.Split(","c).Select(Function(x) x.Trim()).ToArray()]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_467">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String" xml:space="preserve">[JsonConvert.SerializeObject(
    ReqOCRvalues("data").
        Where(Function(p) ArrayTargetPages.Contains(p("PageNo").ToString())).
        Select(Function(p) New With {
            Key .OCR_text = p("OCR_text").ToString(),
            Key .PageNo = p("PageNo").ToString()
        })
)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_13">
                            <iad:CommentOut.Activities>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_466">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[String.Join(Environment.NewLine,
  ReqOCRvalues("data").
    Where(Function(p) ArrayTargetPages.Contains(p("PageNo").ToString())).
    Select(Function(p) p("OCR_text").ToString()))]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </iad:CommentOut.Activities>
                          </iad:CommentOut>
                        </Sequence>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_255">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[page]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Joutitem.Value(1)(0).Tostring]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iaw:OpenBrowser ErrorCode="{x:Null}" Browser="Chrome" ContinueOnError="True" DisplayName="Open Browser" sap2010:WorkflowViewState.IdRef="OpenBrowser_8" PrivateBrowsingEnabled="False" URL="https://www.google.com/" ZoomPercentage="60" />
                        <iaw:NavigateTo ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Navigate To" sap2010:WorkflowViewState.IdRef="NavigateTo_8" URL="[Path.GetDirectoryName(item) + &quot;/&quot; + Uri.EscapeDataString(Path.GetFileName(item)) + &quot;#page=&quot;+page]" WaitAfter="0" WaitBefore="0" ZoomPercentage="100" />
                        <iaw:MaximizeWindow ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Maximize Window" sap2010:WorkflowViewState.IdRef="MaximizeWindow_8" WaitAfter="0" WaitBefore="0" />
                        <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_22" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="0" />
                        <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_23" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                        <iad1:SendKeys ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Send Keys" sap2010:WorkflowViewState.IdRef="SendKeys_24" IsWeb="False" Text="^-" WaitAfter="0" WaitBefore="1000" />
                        <iaw:ScreenShot ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Take Screenshot" sap2010:WorkflowViewState.IdRef="ScreenShot_8" SaveLocation="[screenshotPath]" WaitAfter="0" WaitBefore="10000" />
                        <ias:FileToBase64 ErrorCode="{x:Null}" Base64="[base64string]" ContinueOnError="True" DisplayName="File to Base64 String" FilePath="[screenshotPath]" sap2010:WorkflowViewState.IdRef="FileToBase64_8" />
                        <iaw:CloseBrowser ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Close Browser" sap2010:WorkflowViewState.IdRef="CloseBrowser_8" />
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_490">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[customTextSplit]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">
                              <Literal x:TypeArguments="x:String" Value="" />
                            </InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_3" IsValid="[blncustomExists1]" Path="[strSplitExtension]" />
                        <If Condition="[blncustomExists1]" sap2010:WorkflowViewState.IdRef="If_72">
                          <If.Then>
                            <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_27" Source="[strSplitExtension]" Text="[customTextSplit]" />
                          </If.Then>
                        </If>
                        <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_15" Source="[promptPath2]" Text="[Split2promptText]" />
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_470">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[GenAIModel]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[miscValues("GenAIModel").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_471">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[GenAIModelVersion]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[miscValues("GenAIModelVersion").ToString]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_24">
                          <iad:CommentOut.Activities>
                            <Sequence DisplayName="Prompt API Sequence" sap2010:WorkflowViewState.IdRef="Sequence_150">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_256">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[Split2promptText.Replace("{text}",strSpecificText)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_257">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Replace("{prompt1_response}",Joutitem.tostring)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_258">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Replace("'","\'")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity - 3.5" sap2010:WorkflowViewState.IdRef="Template_Apply_12" Template="{}{ &#xA;'config': { &#xA;'temperature': 0, &#xA;'max_response':4096,&#xA;'stop_sequence': [&#xA;                      'Explanations','Explanation'&#xA;                ]   &#xA;}, &#xA;'prompt':'{{%prompt%}}',&#xA; 'model': '{{%model%}}',   'version': '{{%version%}}'&#xA;" Text="[IonBody1]">
                                <ias:Template_Apply.Values>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>prompt</x:String>
                                      <x:String>base64string</x:String>
                                      <x:String>model</x:String>
                                      <x:String>version</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>SplitpromptRequest</x:String>
                                      <x:String>base64string</x:String>
                                      <x:String>GenAIModel</x:String>
                                      <x:String>GenAIModelVersion</x:String>
                                    </scg:List>
                                  </scg:List>
                                </ias:Template_Apply.Values>
                              </ias:Template_Apply>
                            </Sequence>
                          </iad:CommentOut.Activities>
                        </iad:CommentOut>
                        <Sequence DisplayName="Messages API Sequence - with Image" sap2010:WorkflowViewState.IdRef="Sequence_151">
                          <Assign DisplayName="1Assign" sap2010:WorkflowViewState.IdRef="Assign_485">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[Split2promptText.Replace("{notes}",customTextSplit.tostring)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign DisplayName="2Assign" sap2010:WorkflowViewState.IdRef="Assign_486">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Replace("'","\'")]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign DisplayName="3Assign" sap2010:WorkflowViewState.IdRef="Assign_487">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[outputStructure]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Split(New String() {"====="}, StringSplitOptions.None)(1)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign DisplayName="4Assign" sap2010:WorkflowViewState.IdRef="Assign_488">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[SplitpromptRequest]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[SplitpromptRequest.Split(New String() {"====="}, StringSplitOptions.None)(0)]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign DisplayName="5Assign" sap2010:WorkflowViewState.IdRef="Assign_489">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[value]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[Regex.Replace(strSpecificText.ToString(), "[^A-Za-z0-9\s!@#$%^&amp;*()_+={}\[\]:;,'&lt;&gt;.?//|-]", String.Empty).Replace("'","\'")]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Latest Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_15" Template="{}{&#xA;  'model': '{{%model%}}',&#xA;  'version': '{{%version%}}',&#xA;  'temperature': 0,&#xA;  'max_response': 8192,&#xA;  'stop_sequence': [&#xA;    'Explanations',&#xA;    'Explanation'&#xA;  ],&#xA;  'system': 'You are an expert document based Question-Answering tool. Your task is to answer the 2 questions using the image of only the first page in the document as reference for layout and empty values, but the extraction of values must be from the raw text.',&#xA;  'messages': [&#xA;      {&#xA;        'role': 'user',&#xA;        'content': [&#xA;          {&#xA;            'type': 'image',&#xA;            'data': 'data:image/png;base64,{{%base64string%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Read the following instructions'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%prompt%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%ocrText%}}'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Use the following output format for your response. Your response should only have the JSON without code blocks as the response. Do not include any of your analysis statements before or after the JSON in the response. Do not add any additional objects to the JSON that are not present in the following Output Format Structure.'&#xA;          },&#xA;          {&#xA;            'type': 'text',&#xA;            'data': '{{%outputStructure%}}'&#xA;          }&#xA;        ]&#xA;      },&#xA;      {&#xA;        'role': 'assistant',&#xA;        'content': [&#xA;          {&#xA;            'type': 'text',&#xA;            'data': 'Based on the invoice document, here are the extracted values in the requested JSON format without any code blocks:'&#xA;          }&#xA;        ]&#xA;      }&#xA;  ]&#xA;}&#xA;" Text="[IonBody1]">
                            <ias:Template_Apply.Values>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                  <x:String>prompt</x:String>
                                  <x:String>base64string</x:String>
                                  <x:String>model</x:String>
                                  <x:String>version</x:String>
                                  <x:String>ocrText</x:String>
                                  <x:String>outputStructure</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                  <x:String>SplitpromptRequest</x:String>
                                  <x:String>base64string</x:String>
                                  <x:String>GenAIModel</x:String>
                                  <x:String>GenAIModelVersion</x:String>
                                  <x:String>value</x:String>
                                  <x:String>outputStructure</x:String>
                                </scg:List>
                              </scg:List>
                            </ias:Template_Apply.Values>
                          </ias:Template_Apply>
                        </Sequence>
                        <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON3" sap2010:WorkflowViewState.IdRef="DeserializeJSON_23" JTokenObject="[SplitgenAIRequestToken]" JTokenString="[IonBody1]" />
                        <iai:IONAPIRequestWizard FileAttachments="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" PostData="[SplitgenAIRequestToken]" Response="[genAIResponse1]" ResponseCode="[genAIRespCode]" Url="[tenantID + &quot;genai/chatsvc/api/v1/messages&quot;]">
                          <iai:IONAPIRequestWizard.Headers>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>x-infor-logicalidprefix</x:String>
                                <x:String>Accept</x:String>
                              </scg:List>
                              <scg:List x:TypeArguments="x:String" Capacity="4">
                                <x:String>lid://infor.colemanddp</x:String>
                                <x:String>application/json</x:String>
                              </scg:List>
                            </scg:List>
                          </iai:IONAPIRequestWizard.Headers>
                          <iai:IONAPIRequestWizard.QueryParameters>
                            <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                              <scg:List x:TypeArguments="x:String" Capacity="0" />
                              <scg:List x:TypeArguments="x:String" Capacity="0" />
                            </scg:List>
                          </iai:IONAPIRequestWizard.QueryParameters>
                        </iai:IONAPIRequestWizard>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_259">
                          <Assign.To>
                            <OutArgument x:TypeArguments="njl:JToken">[out3]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(genAIResponse1.ReadAsText)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_260">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[out4]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[out3("content").ToString.Replace("\n","")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_261">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[out4]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[(out4.ToString.Replace("None","''")).Replace("''''","''")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON4" sap2010:WorkflowViewState.IdRef="DeserializeJSON_25" JTokenObject="[jout1]" JTokenString="[out4.ToString]" />
                       <ForEach x:TypeArguments="njl:JProperty" DisplayName="ForEach&lt;JProperty&gt; from GenAISPlit2 Output" sap2010:WorkflowViewState.IdRef="ForEach`1_49" Values="[(JObject.Parse(jout1.ToString)).Properties()]">
                          <ActivityAction x:TypeArguments="njl:JProperty">
                            <ActivityAction.Argument>
                              <DelegateInArgument x:TypeArguments="njl:JProperty" Name="jout1item" />
                            </ActivityAction.Argument>
                            <Sequence DisplayName="Inside ForEach JProperty 2 Sequence" sap2010:WorkflowViewState.IdRef="Sequence_102">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="x:String" Name="CurInvPageNoStart" />
                                <Variable x:TypeArguments="s:String[]" Name="CurVendorSupportingPages" />
                                <Variable x:TypeArguments="x:String" Name="CurInvSupportingPages" />
                              </Sequence.Variables>
                              <Assign DisplayName="Assign CurInvoiceNumber" sap2010:WorkflowViewState.IdRef="Assign_262">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[jout1item.Name.ToString]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign CurInvPageNoAll" sap2010:WorkflowViewState.IdRef="Assign_301">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[CurInvPageNoAll]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[String.Join(",",(jout1item.value))]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign CurInvPageNoStart" sap2010:WorkflowViewState.IdRef="Assign_305">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[CurInvPageNoStart]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[(jout1item.value)(0).tostring]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign CurVendorSupportingPages" sap2010:WorkflowViewState.IdRef="Assign_277">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="s:String[]">[CurVendorSupportingPages]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="s:String[]">[Joutitem.Value(2).ToObject(Of String())()]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign strCurVendorSupportingPages" sap2010:WorkflowViewState.IdRef="Assign_304">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[strCurVendorSupportingPages]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[String.Join(",", CurVendorSupportingPages.SelectMany(Function(s) s.Split(","c)).ToArray())]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign CurInvSupportingPages" sap2010:WorkflowViewState.IdRef="Assign_461">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[CurInvSupportingPages]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[String.Join(","c, CurVendorSupportingPages.Where(Function(p) p.Contains("_") AndAlso p.Split("_"c)(0).Trim() = CurInvPageNoStart.Trim()).Select(Function(p) p.Split("_"c)(1).Trim()))]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign CurInvSupportingPages" sap2010:WorkflowViewState.IdRef="Assign_309">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[CurInvSupportingPages]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[CurInvSupportingPages.Replace("''", "")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign NameForInvoice" sap2010:WorkflowViewState.IdRef="Assign_279">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[NameForInvoice]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["{"+CurInvoiceNumber+"~"+strVendName+"~"+CurInvPageNoAll+"~"+CurInvSupportingPages]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign DisplayName="Assign FinalPages" sap2010:WorkflowViewState.IdRef="Assign_307">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[FinalPages]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(String.IsNullOrWhiteSpace(CurInvSupportingPages),
   CurInvPageNoAll,
   String.Join(","c, (CurInvPageNoAll + "," + CurInvSupportingPages).Split(","c).Select(Function(x) x.Trim()).Distinct())
)]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_20" MethodName="Add">
                                <InvokeMethod.TargetObject>
                                  <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoicePageDictionary]</InArgument>
                                </InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="x:String">[NameForInvoice]</InArgument>
                                <InArgument x:TypeArguments="x:String">[FinalPages.replace(",","-")]</InArgument>
                              </InvokeMethod>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_298">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Boolean">[blnInvDictExists]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </ActivityAction>
                        </ForEach>
                      </Sequence>
                      <Sequence x:Key="NotInvoice" DisplayName="Not Invoice Sequence" sap2010:WorkflowViewState.IdRef="Sequence_144">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_460">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strVendName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Joutitem.Name.Tostring]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_458">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">Not Invoice Others</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Switch x:TypeArguments="x:String" DisplayName="Unstructured Expense Document" Expression="[Joutitem.Value(3).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_11">
                          <Switch.Default>
                            <Sequence DisplayName="Not Invoice - Others" sap2010:WorkflowViewState.IdRef="Sequence_146">
                              <Sequence.Variables>
                                <Variable x:TypeArguments="x:String" Name="Page" />
                                <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                                <Variable x:TypeArguments="x:String" Name="base64string" />
                                <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                                <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                                <Variable x:TypeArguments="x:String" Name="IonBody1" />
                                <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                                <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                                <Variable x:TypeArguments="njl:JToken" Name="out3" />
                                <Variable x:TypeArguments="x:String" Name="out4" />
                                <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                                <Variable x:TypeArguments="x:String" Name="CurInvoiceNumber" />
                                <Variable x:TypeArguments="x:String" Name="CurInvPageNoStart" />
                              </Sequence.Variables>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_472">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["Not Invoice Others"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_473">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[CurInvPageNoStart]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[String.Join(",", CType(Joutitem.Value(1), JArray).Select(Function(x) x.ToString()).ToArray())]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_474">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[NameForInvoice]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">["{"+CurInvoiceNumber+"~"+strVendName+"-"+UnstructuredCounter.tostring+"~"+CurInvPageNoStart+"~"]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_475">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[FinalPages]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[CurInvPageNoStart]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_478">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".pdf",NameForInvoice+".pdf")]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(pdfFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_71">
                                <If.Then>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_479">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(pdfFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </If.Then>
                              </If>
                              <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_15" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\InProgress&quot;]" TargetFilename="[pdfFileName]" />
                              <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_15" Source="[item]" />
                            </Sequence>
                          </Switch.Default>
                          <Sequence x:Key="Unstructured Expense Document" DisplayName="Not Invoice - Unstructured Expense Document Sequence" sap2010:WorkflowViewState.IdRef="Sequence_104">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="Page" />
                              <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                              <Variable x:TypeArguments="x:String" Name="base64string" />
                              <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                              <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                              <Variable x:TypeArguments="x:String" Name="IonBody1" />
                              <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                              <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                              <Variable x:TypeArguments="njl:JToken" Name="out3" />
                              <Variable x:TypeArguments="x:String" Name="out4" />
                              <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                              <Variable x:TypeArguments="x:String" Name="CurInvoiceNumber" />
                              <Variable x:TypeArguments="x:String" Name="CurInvPageNoStart" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_288">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["Unstructured Expense Document"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_302">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[CurInvPageNoStart]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[String.Join(",", CType(Joutitem.Value(1), JArray).Select(Function(x) x.ToString()).ToArray())]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_292">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[NameForInvoice]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["{"+CurInvoiceNumber+"~"+strVendName+"-"+UnstructuredCounter.tostring+"~"+CurInvPageNoStart+"~"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_297">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[FinalPages]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[CurInvPageNoStart]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_21" MethodName="Add">
                              <InvokeMethod.TargetObject>
                                <InArgument x:TypeArguments="scg:Dictionary(x:String, x:String)">[invoicePageDictionary]</InArgument>
                              </InvokeMethod.TargetObject>
                              <InArgument x:TypeArguments="x:String">[NameForInvoice]</InArgument>
                              <InArgument x:TypeArguments="x:String">[FinalPages.replace(",","-")]</InArgument>
                            </InvokeMethod>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_310">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[UnstructuredCounter]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[UnstructuredCounter+1]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_299">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Boolean">[blnInvDictExists]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </Switch>
                      </Sequence>
                    </Switch>
                  </Sequence>
                </ActivityAction>
              </ForEach>
              <Switch x:TypeArguments="x:Boolean" DisplayName="Rename .PDF files if any" Expression="[item.contains(&quot;.PDF&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_12">
                <Sequence x:Key="True" DisplayName="PDF Sequence" sap2010:WorkflowViewState.IdRef="Sequence_105">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                    <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                  </Sequence.Variables>
                  <Assign DisplayName="Assign pdfFileName" sap2010:WorkflowViewState.IdRef="Assign_293">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".PDF","_copy.pdf")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_1" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[Path.GetDirectoryName(item)]" TargetFilename="[pdfFileName]" />
                  <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_1" Source="[item]" />
                  <Assign DisplayName="Assign attachment+_Copy" sap2010:WorkflowViewState.IdRef="Assign_294">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[item.replace(".PDF","_copy.pdf")]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </Switch>
              <If Condition="[blnInvDictExists]" DisplayName="Check if invoicePageDictionary exists" sap2010:WorkflowViewState.IdRef="If_26">
                <If.Then>
                  <If Condition="[(invoicePageDictionary.count)&gt;1]" DisplayName="Check Dictionary Count" sap2010:WorkflowViewState.IdRef="If_25">
                    <If.Then>
                      <iaw1:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From{{&quot;invoiceDictionary&quot;,invoicePageDictionary},{&quot;logFile&quot;,logFile},{&quot;InProgressFolder&quot;,configurationFolder+&quot;\InProgress&quot;},{&quot;attachment&quot;,item},{&quot;tenantID&quot;,tenantID},{&quot;enableMessageBoxes&quot;,enableMessageBoxes}}]" ContinueOnError="False" DisplayName="ProcessDocument" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_2" WorkflowFile="[projectPath+&quot;\ProcessDocument.xaml&quot;]" />
                    </If.Then>
                    <If.Else>
                      <Sequence DisplayName="invoicePageDictionary.count not &gt;1" sap2010:WorkflowViewState.IdRef="Sequence_106">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_295">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".pdf",NameForInvoice+".pdf")]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(pdfFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_24">
                          <If.Then>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_296">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(pdfFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                        </If>
                        <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_2" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\InProgress&quot;]" TargetFilename="[pdfFileName]" />
                        <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_2" Source="[item]" />
                      </Sequence>
                    </If.Else>
                  </If>
                </If.Then>
              </If>
            </Sequence>
          </ActivityAction>
        </ForEach>
      </Sequence>
      <Sequence x:Key="False" DisplayName="Split False Sequence" sap2010:WorkflowViewState.IdRef="Sequence_110">
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Get OCR Files in OCR Data" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_4" IncludeSubDir="True" />
        <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\ClassificationData&quot;]" DisplayName="Get Classification Data Files in ClassificationData" FileType="All" Files="[ListClassificationData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_5" IncludeSubDir="True" />
        <ForEach x:TypeArguments="x:String" DisplayName="ForEach PDF in MasterDownloads folder" sap2010:WorkflowViewState.IdRef="ForEach`1_52" Values="[MasterDownloadedFiles]">
          <ActivityAction x:TypeArguments="x:String">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="x:String" Name="item" />
            </ActivityAction.Argument>
            <Sequence DisplayName="Inside ForEach PDF in MasterDownloads folder Sequence" sap2010:WorkflowViewState.IdRef="Sequence_125">
              <Assign sap2010:WorkflowViewState.IdRef="Assign_312">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
  ListOCRData.
    Where(Function(f) Path.GetFileNameWithoutExtension(f).Equals(Path.GetFileNameWithoutExtension(item), StringComparison.OrdinalIgnoreCase)).
    FirstOrDefault(),
  "NA"
)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_313">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[CurrentClassificationFIle]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(
  ListClassificationData.
    Where(Function(f) Path.GetFileNameWithoutExtension(f).Equals(Path.GetFileNameWithoutExtension(item), StringComparison.OrdinalIgnoreCase)).
    FirstOrDefault(),
  "NA"
)]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read OCR File" sap2010:WorkflowViewState.IdRef="File_Read_25" Source="[CurrentOCRFile]" Text="[OCRText]" />
              <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read Classification File" sap2010:WorkflowViewState.IdRef="File_Read_26" Source="[CurrentClassificationFIle]" Text="[ClassificationText]" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_416">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[values]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRText)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_417">
                <Assign.To>
                  <OutArgument x:TypeArguments="njl:JToken">[Jout]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(ClassificationText)]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_311">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[noOfPages]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[values("_metadata")("NumberOfPages").ToString]</InArgument>
                </Assign.Value>
              </Assign>
              <ForEach x:TypeArguments="njl:JProperty" DisplayName="From Classification Output Invoice/NotInvoice" sap2010:WorkflowViewState.IdRef="ForEach`1_58" Values="[(JObject.Parse(jout.ToString)).Properties()]">
                <ActivityAction x:TypeArguments="njl:JProperty">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="njl:JProperty" Name="Joutitem" />
                  </ActivityAction.Argument>
                  <Sequence DisplayName="Inside Check Invoice/NotInvoice" sap2010:WorkflowViewState.IdRef="Sequence_136">
                    <Switch x:TypeArguments="x:String" DisplayName="Check Item Type" Expression="[Joutitem.Value(0).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_23">
                      <Sequence x:Key="Invoice" DisplayName="Invoice Sequence When Split False" sap2010:WorkflowViewState.IdRef="Sequence_134">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="Page" />
                          <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                          <Variable x:TypeArguments="x:String" Name="base64string" />
                          <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                          <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                          <Variable x:TypeArguments="x:String" Name="IonBody1" />
                          <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                          <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                          <Variable x:TypeArguments="njl:JToken" Name="out3" />
                          <Variable x:TypeArguments="x:String" Name="out4" />
                          <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_455">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">
                              <Literal x:TypeArguments="x:String" Value="" />
                            </InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                      <Sequence x:Key="NotInvoice" DisplayName="Not Invoice Sequence" sap2010:WorkflowViewState.IdRef="Sequence_143">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_456">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">Not Invoice Others</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Switch x:TypeArguments="x:String" DisplayName="Notinvoice Sequence When Split False Unstructured Expense Document" Expression="[Joutitem.Value(3).ToString]" sap2010:WorkflowViewState.IdRef="Switch`1_22">
                          <Sequence x:Key="Unstructured Expense Document" DisplayName="Not Invoice - Unstructured Expense Document Sequence" sap2010:WorkflowViewState.IdRef="Sequence_135">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:String" Name="Page" />
                              <Variable x:TypeArguments="x:String" Name="screenshotPath" />
                              <Variable x:TypeArguments="x:String" Name="base64string" />
                              <Variable x:TypeArguments="x:String" Name="Split2promptText" />
                              <Variable x:TypeArguments="x:String" Name="SplitpromptRequest" />
                              <Variable x:TypeArguments="x:String" Name="IonBody1" />
                              <Variable x:TypeArguments="njl:JToken" Name="SplitgenAIRequestToken" />
                              <Variable x:TypeArguments="iru:ResponseObject" Name="genAIResponse1" />
                              <Variable x:TypeArguments="njl:JToken" Name="out3" />
                              <Variable x:TypeArguments="x:String" Name="out4" />
                              <Variable x:TypeArguments="njl:JToken" Name="jout1" />
                              <Variable x:TypeArguments="x:String" Name="CurInvPageNoStart" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_436">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[CurInvoiceNumber]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">["Unstructured Expense Document"]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </Switch>
                      </Sequence>
                    </Switch>
                  </Sequence>
                </ActivityAction>
              </ForEach>
              <Switch x:TypeArguments="x:Boolean" DisplayName="Rename .PDF files if any" Expression="[item.contains(&quot;.PDF&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_26">
                <Sequence x:Key="True" DisplayName="PDF Sequence" sap2010:WorkflowViewState.IdRef="Sequence_141">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                    <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                  </Sequence.Variables>
                  <Assign DisplayName="Assign pdfFileName" sap2010:WorkflowViewState.IdRef="Assign_451">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".PDF","_copy.pdf")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_13" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[Path.GetDirectoryName(item)]" TargetFilename="[pdfFileName]" />
                  <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_13" Source="[item]" />
                  <Assign DisplayName="Assign attachment+_Copy" sap2010:WorkflowViewState.IdRef="Assign_452">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[item.replace(".PDF","_copy.pdf")]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </Switch>
              <Sequence DisplayName="Move Files to Inprogress" sap2010:WorkflowViewState.IdRef="Sequence_142">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                </Sequence.Variables>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_453">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".pdf","{"+CurInvoiceNumber+".pdf")]</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(pdfFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_69">
                  <If.Then>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_454">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(pdfFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
                <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_14" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\InProgress&quot;]" TargetFilename="[pdfFileName]" />
                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_14" Source="[item]" />
              </Sequence>
            </Sequence>
          </ActivityAction>
        </ForEach>
      </Sequence>
    </Switch>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Directory_GetFiles_1" sap:VirtualizedContainerService.HintSize="529.333333333333,22" />
      <sap2010:ViewStateData Id="File_Read_4" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="DocumentOC_2" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="File_Create_3" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Path_Validate_2" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="File_Read_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_23" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_468" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Assign_469" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_148" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_22" sap:VirtualizedContainerService.HintSize="264,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_480" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_481" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_482" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_483" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_484" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_14" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_25" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_16" sap:VirtualizedContainerService.HintSize="242,46.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_149" sap:VirtualizedContainerService.HintSize="264,778.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DeserializeJSON_9" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="264,62" />
      <sap2010:ViewStateData Id="DeserializeJSON_10" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="File_Create_4" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="264,22" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="286,2179.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_3" sap:VirtualizedContainerService.HintSize="476.666666666667,2370.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="498.666666666667,2742.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_33" sap:VirtualizedContainerService.HintSize="529.333333333333,2895.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="776,3081.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_2" sap:VirtualizedContainerService.HintSize="734,22" />
      <sap2010:ViewStateData Id="Directory_GetFiles_3" sap:VirtualizedContainerService.HintSize="734,22" />
      <sap2010:ViewStateData Id="Assign_308" sap:VirtualizedContainerService.HintSize="681.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_231" sap:VirtualizedContainerService.HintSize="681.333333333333,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_232" sap:VirtualizedContainerService.HintSize="681.333333333333,65.3333333333333" />
      <sap2010:ViewStateData Id="File_Read_12" sap:VirtualizedContainerService.HintSize="681.333333333333,22" />
      <sap2010:ViewStateData Id="File_Read_13" sap:VirtualizedContainerService.HintSize="681.333333333333,22" />
      <sap2010:ViewStateData Id="Assign_300" sap:VirtualizedContainerService.HintSize="681.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_233" sap:VirtualizedContainerService.HintSize="681.333333333333,62" />
      <sap2010:ViewStateData Id="MessageBox_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_20" sap:VirtualizedContainerService.HintSize="628.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_459" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_462" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_463" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_464" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_467" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_466" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_13" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_145" sap:VirtualizedContainerService.HintSize="586.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_255" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="OpenBrowser_8" sap:VirtualizedContainerService.HintSize="586.666666666667,58" />
      <sap2010:ViewStateData Id="NavigateTo_8" sap:VirtualizedContainerService.HintSize="586.666666666667,58" />
      <sap2010:ViewStateData Id="MaximizeWindow_8" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_22" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_23" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="SendKeys_24" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="ScreenShot_8" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="FileToBase64_8" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="CloseBrowser_8" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_490" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Path_Validate_3" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="File_Read_27" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_72" sap:VirtualizedContainerService.HintSize="586.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Read_15" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_470" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_471" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_256" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_257" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_258" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_150" sap:VirtualizedContainerService.HintSize="264,452">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_24" sap:VirtualizedContainerService.HintSize="586.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_485" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_486" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_487" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_488" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_489" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Template_Apply_15" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_151" sap:VirtualizedContainerService.HintSize="586.666666666667,656">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DeserializeJSON_23" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_259" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_260" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_261" sap:VirtualizedContainerService.HintSize="586.666666666667,62" />
      <sap2010:ViewStateData Id="MessageBox_66" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_26" sap:VirtualizedContainerService.HintSize="586.666666666667,118" />
      <sap2010:ViewStateData Id="DeserializeJSON_25" sap:VirtualizedContainerService.HintSize="586.666666666667,22" />
      <sap2010:ViewStateData Id="MessageBox_62" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="MessageBox_64" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_15" sap:VirtualizedContainerService.HintSize="586.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_262" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_301" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_305" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_277" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_304" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_461" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_309" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_279" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_307" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="MessageBox_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_17" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_20" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_298" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_102" sap:VirtualizedContainerService.HintSize="264,1379.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_49" sap:VirtualizedContainerService.HintSize="586.666666666667,1532">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_99" sap:VirtualizedContainerService.HintSize="608.666666666667,4647.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_460" sap:VirtualizedContainerService.HintSize="477.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_458" sap:VirtualizedContainerService.HintSize="477.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_472" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_473" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_474" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_475" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_478" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_479" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_71" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Copy_15" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_15" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_146" sap:VirtualizedContainerService.HintSize="264,810.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_288" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_302" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_292" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_297" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_21" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_310" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_299" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_104" sap:VirtualizedContainerService.HintSize="264,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_11" sap:VirtualizedContainerService.HintSize="477.333333333333,143.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_144" sap:VirtualizedContainerService.HintSize="499.333333333333,461.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_8" sap:VirtualizedContainerService.HintSize="628.666666666667,4862">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_107" sap:VirtualizedContainerService.HintSize="650.666666666667,5084">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_46" sap:VirtualizedContainerService.HintSize="681.333333333333,5236.66666666667" />
      <sap2010:ViewStateData Id="Assign_293" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Copy_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_294" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_105" sap:VirtualizedContainerService.HintSize="264,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_12" sap:VirtualizedContainerService.HintSize="681.333333333333,143.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Assign_295" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_296" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_24" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Copy_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_106" sap:VirtualizedContainerService.HintSize="264,399">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_25" sap:VirtualizedContainerService.HintSize="490,552.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_26" sap:VirtualizedContainerService.HintSize="681.333333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_94" sap:VirtualizedContainerService.HintSize="703.333333333333,6277.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_40" sap:VirtualizedContainerService.HintSize="734,6430">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_109" sap:VirtualizedContainerService.HintSize="756,6678">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_4" sap:VirtualizedContainerService.HintSize="624,22" />
      <sap2010:ViewStateData Id="Directory_GetFiles_5" sap:VirtualizedContainerService.HintSize="624,22" />
      <sap2010:ViewStateData Id="Assign_312" sap:VirtualizedContainerService.HintSize="571.333333333333,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_313" sap:VirtualizedContainerService.HintSize="571.333333333333,65.3333333333333" />
      <sap2010:ViewStateData Id="File_Read_25" sap:VirtualizedContainerService.HintSize="571.333333333333,22" />
      <sap2010:ViewStateData Id="File_Read_26" sap:VirtualizedContainerService.HintSize="571.333333333333,22" />
      <sap2010:ViewStateData Id="Assign_416" sap:VirtualizedContainerService.HintSize="571.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_417" sap:VirtualizedContainerService.HintSize="571.333333333333,62" />
      <sap2010:ViewStateData Id="Assign_311" sap:VirtualizedContainerService.HintSize="571.333333333333,62" />
      <sap2010:ViewStateData Id="MessageBox_50" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_21" sap:VirtualizedContainerService.HintSize="518.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_455" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_134" sap:VirtualizedContainerService.HintSize="264,183.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_456" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_436" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_135" sap:VirtualizedContainerService.HintSize="264,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_22" sap:VirtualizedContainerService.HintSize="476.666666666667,249.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_143" sap:VirtualizedContainerService.HintSize="498.666666666667,475.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_23" sap:VirtualizedContainerService.HintSize="518.666666666667,690">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_136" sap:VirtualizedContainerService.HintSize="540.666666666667,912">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_58" sap:VirtualizedContainerService.HintSize="571.333333333333,1064.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_451" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Copy_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_13" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_452" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_141" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_26" sap:VirtualizedContainerService.HintSize="571.333333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_453" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_454" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_69" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Copy_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_14" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_142" sap:VirtualizedContainerService.HintSize="571.333333333333,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="593.333333333333,2014.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_52" sap:VirtualizedContainerService.HintSize="624,2167.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_110" sap:VirtualizedContainerService.HintSize="646,2372.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_27" sap:VirtualizedContainerService.HintSize="776,6892.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="798,10138">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="838,10258" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>