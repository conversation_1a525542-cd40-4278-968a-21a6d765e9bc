﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="emailSubject" Type="InArgument(x:String)" />
    <x:Property Name="emailReceivedTime" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="manualEntry" Type="InArgument(x:Boolean)" />
    <x:Property Name="documentPath" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="status" Type="OutArgument(x:String)" />
    <x:Property Name="statusComments" Type="OutArgument(x:String)" />
    <x:Property Name="ocrResults" Type="InArgument(x:String)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="vendorName" Type="InArgument(x:String)" />
    <x:Property Name="company" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="failureCount" Type="InArgument(x:Int32)" />
    <x:Property Name="notificationFailureCount" Type="InArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="includeDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="invoiceType" Type="InArgument(x:String)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="vendorNames" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="deliveryNumbers" Type="InArgument(scg:List(x:String))" />
    <x:Property Name="GLCode" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="InArgument(x:String)" />
    <x:Property Name="approvalList" Type="InOutArgument(scg:List(x:String))" />
    <x:Property Name="APResp" Type="InOutArgument(x:String)" />
    <x:Property Name="ListocrLineValues" Type="InArgument(scg:List(scg:Dictionary(x:String, x:Object)))" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="processId" Type="OutArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>System.Globalization</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>System.IO</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Linq</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ProcessPOInvoice_Sequence_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="convertedDate" />
      <Variable x:TypeArguments="x:Int32" Name="poInvoiceResponseCode" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="poInvoiceResponseDictionary" />
      <Variable x:TypeArguments="njl:JToken" Name="ocrText" />
      <Variable x:TypeArguments="x:Boolean" Name="processInvoice" />
      <Variable x:TypeArguments="x:String" Name="statusDisp" />
      <Variable x:TypeArguments="x:String" Name="PID" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="idmResponseDictionary" />
      <Variable x:TypeArguments="scg:List(s:String[])" Name="Attribute_List" />
      <Variable x:TypeArguments="x:String" Name="fileName" />
      <Variable x:TypeArguments="x:Int32" Name="attriResp" />
      <Variable x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="LinesDict" />
      <Variable x:TypeArguments="x:String" Name="User_GUID" />
    </Sequence.Variables>
    <Assign DisplayName="ProcessPOInvoice_Assign_notificationFailureCount_2" sap2010:WorkflowViewState.IdRef="Assign_18">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[notificationFailureCount]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">-</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="ProcessPOInvoice_Assign_approvalList_3" sap2010:WorkflowViewState.IdRef="Assign_78">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[new list(Of string)]</InArgument>
      </Assign.Value>
    </Assign>
    <Switch x:TypeArguments="x:String" DisplayName="ProcessPOInvoice_Switch_invoiceType_4" Expression="[invoiceType]" sap2010:WorkflowViewState.IdRef="Switch`1_1">
      <iaw:InvokeWorkflow x:Key="DELIVERYNOTE" Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;logFile&quot;,logFile},{&quot;projectPath&quot;,projectPath},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;division&quot;,division},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;documentPath&quot;,documentPath},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;vendorId&quot;,vendorId}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_DELIVERYNOTE_5" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_23" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[poInvoiceResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessDeliveryNote.xaml&quot;]" />
      <iaw:InvokeWorkflow x:Key="POINVOICE" Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;logFile&quot;,logFile},{&quot;projectPath&quot;,projectPath},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;division&quot;,division},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;ListocrLineValues&quot;,ListocrLineValues},{&quot;vendorId&quot;,vendorId},{&quot;includeDatalake&quot;,includeDatalake}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_POINVOICE_6" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_37" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[poInvoiceResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessPOInvoiceAPI_Ind.xaml&quot;]" />
      <iaw:InvokeWorkflow x:Key="POEXPENSE" Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;colemanAPI&quot;,colemanAPI},{&quot;logFile&quot;,logFile},{&quot;projectPath&quot;,projectPath},{&quot;authUser&quot;,authUser},{&quot;miscValues&quot;,miscValues}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_POEXPENSE_7" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_21" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[poInvoiceResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessExpenseWithPO.xaml&quot;]" />
      <iaw:InvokeWorkflow x:Key="EXPENSE" Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;DictOcrValues&quot;,DictOcrValues},{&quot;logFile&quot;,logFile},{&quot;authUser&quot;,authUser},{&quot;APResp&quot;,APResp},{&quot;vendorId&quot;,vendorId},{&quot;approvalRequired&quot;,approvalRequired},{&quot;projectPath&quot;,projectPath},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;ListocrLineValues&quot;,ListocrLineValues},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;GLCode&quot;,GLCode},{&quot;division&quot;,division}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_EXPENSE_8" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_22" OutputArguments="[poInvoiceResponseDictionary]" ResponseCode="[poInvoiceResponseCode]" WorkflowFile="[projectPath+&quot;\ProcessExpenseInvoice - Copy.xaml&quot;]" />
    </Switch>
    <If Condition="[poInvoiceResponseCode =200]" DisplayName="ProcessPOInvoice_If_poInvoiceResponseCode_9" sap2010:WorkflowViewState.IdRef="If_6">
      <If.Then>
        <Sequence DisplayName="ProcessPOInvoice_Sequence_SuccessSequence_10" sap2010:WorkflowViewState.IdRef="Sequence_11">
          <Assign DisplayName="ProcessPOInvoice_Assign_status_11" sap2010:WorkflowViewState.IdRef="Assign_7">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("status"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="ProcessPOInvoice_Assign_statusComments_12" sap2010:WorkflowViewState.IdRef="Assign_8">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("commentStatus"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="ProcessPOInvoice_Assign_vendorName_13" sap2010:WorkflowViewState.IdRef="Assign_14">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("vendorName"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="ProcessPOInvoice_Assign_approvalList_14" sap2010:WorkflowViewState.IdRef="Assign_79">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[CType(poInvoiceResponseDictionary("approvalList"), List(Of String))]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[vendorId = &quot;&quot;]" DisplayName="ProcessPOInvoice_If_vendorId_15" sap2010:WorkflowViewState.IdRef="If_46">
            <If.Then>
              <Assign DisplayName="ProcessPOInvoice_Assign_vendorId_16" sap2010:WorkflowViewState.IdRef="Assign_15">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("vendorId"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <If Condition="[company = &quot;&quot;]" DisplayName="ProcessPOInvoice_If_company_17" sap2010:WorkflowViewState.IdRef="If_44">
            <If.Then>
              <Assign DisplayName="ProcessPOInvoice_Assign_company_18" sap2010:WorkflowViewState.IdRef="Assign_17">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("company"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <If Condition="[division = &quot;&quot;]" DisplayName="ProcessPOInvoice_If_division_19" sap2010:WorkflowViewState.IdRef="If_43">
            <If.Then>
              <Assign DisplayName="ProcessPOInvoice_Assign_division_20" sap2010:WorkflowViewState.IdRef="Assign_25">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("division"), String)]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <Assign DisplayName="ProcessPOInvoice_Assign_APResp_21" sap2010:WorkflowViewState.IdRef="Assign_99">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[division &lt;&gt; &quot;&quot; AND miscValues(&quot;company&quot;).ToString &lt;&gt; &quot;&quot;]" DisplayName="ProcessPOInvoice_If_divisionAndCompany_22" sap2010:WorkflowViewState.IdRef="If_57">
            <If.Then>
              <Sequence DisplayName="ProcessPOInvoice_Sequence_APRespSequence_23" sap2010:WorkflowViewState.IdRef="Sequence_52">
                <Sequence.Variables>
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="APRespRespDict" />
                  <Variable x:TypeArguments="x:Int32" Name="APRespRespCode" />
                  <Variable x:TypeArguments="x:String" Name="Buyer" />
                  <Variable x:TypeArguments="x:String" Name="APTeam" />
                </Sequence.Variables>
                <Assign DisplayName="Assign - Buyer" sap2010:WorkflowViewState.IdRef="Assign_102">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Buyer]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("Buyer"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Assign - APTeam" sap2010:WorkflowViewState.IdRef="Assign_103">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[APTeam]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("APTeam"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;company&quot;,miscValues(&quot;company&quot;).ToString},{&quot;VendorID&quot;,vendorID},{&quot;Buyer&quot;,Buyer},{&quot;APTeam&quot;,APTeam},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_APResp_24" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_50" OutputArguments="[APRespRespDict]" ResponseCode="[APRespRespCode]" WorkflowFile="[projectPath+&quot;\APResp.xaml&quot;]" />
                <If Condition="[APRespRespCode = 200]" DisplayName="ProcessPOInvoice_If_APRespRespCode_25" sap2010:WorkflowViewState.IdRef="If_56">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_53">
                      <Assign DisplayName="ProcessPOInvoice_Assign_APResp_26" sap2010:WorkflowViewState.IdRef="Assign_100">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[APResp]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("APResp"), String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="ProcessPOInvoice_Assign_User_GUID" sap2010:WorkflowViewState.IdRef="Assign_104">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[User_GUID]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(APRespRespDict("User_GUID"), String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
          </If>
          <If Condition="[vendorName = &quot;&quot;]" DisplayName="ProcessPOInvoice_If_vendorName_27" sap2010:WorkflowViewState.IdRef="If_11">
            <If.Then>
              <Assign DisplayName="ProcessPOInvoice_Assign_vendorName_28" sap2010:WorkflowViewState.IdRef="Assign_16">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[DictOcrValues("VENDOR_NAME").ToString]</InArgument>
                </Assign.Value>
              </Assign>
            </If.Then>
          </If>
          <Assign DisplayName="ProcessPOInvoice_Assign_vendorNameReplace_29" sap2010:WorkflowViewState.IdRef="Assign_22">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vendorName]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[vendorName.Replace(",","")]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[enableMessageBoxes]" DisplayName="ProcessPOInvoice_If_enableMessageBoxes_30" sap2010:WorkflowViewState.IdRef="If_10">
            <If.Then>
              <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="ProcessPOInvoice_MessageBox_status_31" sap2010:WorkflowViewState.IdRef="MessageBox_2" Selection="OK" Text="[&quot;Status : &quot;+status+&quot; Comments :&quot;+statusComments]" />
            </If.Then>
          </If>
          <If Condition="[extractFromWidgetDatalake]" DisplayName="ProcessPOInvoice_If_extractFromWidgetDatalake_32" sap2010:WorkflowViewState.IdRef="If_18">
            <If.Then>
              <If Condition="[NOT includeDatalake]" DisplayName="ProcessPOInvoice_If_includeDatalake_33" sap2010:WorkflowViewState.IdRef="If_19">
                <If.Then>
                  <Sequence DisplayName="ProcessPOInvoice_Sequence_DatalakeSequence_34" sap2010:WorkflowViewState.IdRef="Sequence_17">
                    <If Condition="[status = &quot;PONOTRECEIVED&quot;]" DisplayName="ProcessPOInvoice_If_statusPONOTRECEIVED_35" sap2010:WorkflowViewState.IdRef="If_20">
                      <If.Then>
                        <Sequence DisplayName="ProcessPOInvoice_Sequence_PONotReceivedSequence_36" sap2010:WorkflowViewState.IdRef="Sequence_30">
                          <Assign DisplayName="ProcessPOInvoice_Assign_failureCount_37" sap2010:WorkflowViewState.IdRef="Assign_35">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Int32">[failureCount]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Int32">1</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign DisplayName="ProcessPOInvoice_Assign_statusDisp_38" sap2010:WorkflowViewState.IdRef="Assign_39">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[statusDisp]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">Pending Process</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <Sequence DisplayName="ProcessPOInvoice_Sequence_ElseSequence_39" sap2010:WorkflowViewState.IdRef="Sequence_31">
                          <Assign DisplayName="ProcessPOInvoice_Assign_failureCount_40" sap2010:WorkflowViewState.IdRef="Assign_34">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:Int32">[failureCount]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:Int32">0</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign DisplayName="ProcessPOInvoice_Assign_statusDisp_41" sap2010:WorkflowViewState.IdRef="Assign_38">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[statusDisp]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[Status]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </Sequence>
                      </If.Else>
                    </If>
                    <Sequence DisplayName="ProcessPOInvoice_Sequence_BankAccountSequence_42" sap2010:WorkflowViewState.IdRef="Sequence_29">
                      <If Condition="[statusComments.Contains(&quot;Bank account identity must be entered&quot;)]" DisplayName="ProcessPOInvoice_If_bankAccount_43" sap2010:WorkflowViewState.IdRef="If_42">
                        <If.Then>
                          <Assign DisplayName="ProcessPOInvoice_Assign_statusNeedsVerification_44" sap2010:WorkflowViewState.IdRef="Assign_48">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                    </Sequence>
                  </Sequence>
                </If.Then>
              </If>
            </If.Then>
          </If>
          <If Condition="[Status = &quot;SUCCESS&quot;]" DisplayName="ProcessPOInvoice_If_statusSuccess_45" sap2010:WorkflowViewState.IdRef="If_38">
            <If.Then>
              <Sequence DisplayName="ProcessPOInvoice_Sequence_IDMSequence_46" sap2010:WorkflowViewState.IdRef="Sequence_34">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="correlationID" />
                  <Variable x:TypeArguments="x:String" Name="inYear" />
                  <Variable x:TypeArguments="x:String" Name="AYear" />
                </Sequence.Variables>
                <Assign DisplayName="ProcessPOInvoice_Assign_correlationID_47" sap2010:WorkflowViewState.IdRef="Assign_45">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[correlationID]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("correlationID"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="ProcessPOInvoice_Assign_AYear_48" sap2010:WorkflowViewState.IdRef="Assign_52">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[AYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("AYear"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="ProcessPOInvoice_Assign_InYear_49" sap2010:WorkflowViewState.IdRef="Assign_53">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[InYear]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("InYear"), String)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,documentPath},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;companyNumber&quot;,company},{&quot;supplierNo&quot;,vendorId},{&quot;documentType&quot;,&quot;M3_SupplierInvoice&quot;},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},{&quot;division&quot;,division},{&quot;correlationID&quot;,correlationID},{&quot;InYear&quot;,InYear},{&quot;AYear&quot;,AYear}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_IDM_50" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_32" WorkflowFile="[projectPath+&quot;\SendToIDM.xaml&quot;]" />
              </Sequence>
            </If.Then>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence DisplayName="ProcessPOInvoice_Sequence_FailureSequence_51" sap2010:WorkflowViewState.IdRef="Sequence_13">
          <Assign DisplayName="ProcessPOInvoice_Assign_statusFailure_52" sap2010:WorkflowViewState.IdRef="Assign_9">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="ProcessPOInvoice_Assign_statusCommentsException_53" sap2010:WorkflowViewState.IdRef="Assign_10">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Exception occured while processing the request"]</InArgument>
            </Assign.Value>
          </Assign>
        </Sequence>
      </If.Else>
    </If>
    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ProcessPOInvoice_AppendLine_statusComments_54" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[&quot;Status : &quot;+status+&quot; Comments :&quot;+statusComments]" Source="[logFile]" />
    <If Condition="[extractFromWidgetDatalake AND NOT includeDatalake]" DisplayName="ProcessPOInvoice_If_extractAndNotIncludeDatalake_55" sap2010:WorkflowViewState.IdRef="If_51">
      <If.Then>
        <Sequence DisplayName="ProcessPOInvoice_Sequence_WidgetDatalakeSequence_56" sap2010:WorkflowViewState.IdRef="Sequence_46">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:String" Name="TaxString" />
            <Variable x:TypeArguments="x:String" Name="issueLines" />
          </Sequence.Variables>
          <Assign DisplayName="ProcessPOInvoice_Assign_fileName_57" sap2010:WorkflowViewState.IdRef="Assign_65">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[Path.GetFileName(documentPath).Replace("'","").Replace("""","")]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="ProcessPOInvoice_Assign_processId_58" sap2010:WorkflowViewState.IdRef="Assign_66">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[processId]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[fileName.substring(0,fileName.indexOf("."))+System.DateTime.Now.ToString("yyyyMMddHHmmssfff").tostring]</InArgument>
            </Assign.Value>
          </Assign>
          <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,documentPath},{&quot;documentType&quot;,&quot;RPAExceptionHandling&quot;},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID},{&quot;processID&quot;,processId}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_WidgetIDM_59" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_42" OutputArguments="[idmResponseDictionary]" WorkflowFile="[projectPath+&quot;\SendToWidgetIDM.xaml&quot;]" />
          <Assign DisplayName="ProcessPOInvoice_Assign_issueLines_60" sap2010:WorkflowViewState.IdRef="Assign_101">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[issueLines]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(poInvoiceResponseDictionary("issueLines"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="ProcessPOInvoice_Assign_PID_62" sap2010:WorkflowViewState.IdRef="Assign_67">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[PID]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[CType(idmResponseDictionary("PID"), String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Sequence DisplayName="ProcessPOInvoice_Sequence_TaxSequence_63" sap2010:WorkflowViewState.IdRef="Sequence_48">
            <If Condition="[DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString &lt;&gt; &quot;&quot;]" DisplayName="ProcessPOInvoice_If_VATAmount_64" sap2010:WorkflowViewState.IdRef="If_55">
              <If.Then>
                <Sequence DisplayName="ProcessPOInvoice_Sequence_VATSequence_65" sap2010:WorkflowViewState.IdRef="Sequence_49">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:Int32" Name="TaxAmtCount" />
                    <Variable x:TypeArguments="x:Int32" Name="i" />
                    <Variable x:TypeArguments="x:String" Name="TaxPercentString" />
                    <Variable x:TypeArguments="x:String" Name="TaxAmountString" />
                  </Sequence.Variables>
                  <Assign DisplayName="ProcessPOInvoice_Assign_TaxAmtCount_66" sap2010:WorkflowViewState.IdRef="Assign_89">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[TaxAmtCount]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[DictOcrValues("VAT_AMOUNT").ToString.split(","c).Count]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="ProcessPOInvoice_Assign_i_67" sap2010:WorkflowViewState.IdRef="Assign_90">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">0</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="ProcessPOInvoice_Assign_TaxString_68" sap2010:WorkflowViewState.IdRef="Assign_91">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[TaxString]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">
                        <Literal x:TypeArguments="x:String" Value="" />
                      </InArgument>
                    </Assign.Value>
                  </Assign>
                  <While DisplayName="ProcessPOInvoice_While_TaxLoop_69" sap2010:WorkflowViewState.IdRef="While_1" Condition="[i &lt; TaxAmtCount]">
                    <Sequence DisplayName="ProcessPOInvoice_Sequence_WhileLoopSequence_70" sap2010:WorkflowViewState.IdRef="Sequence_50">
                      <TryCatch DisplayName="ProcessPOInvoice_TryCatch_TaxAmount_71" sap2010:WorkflowViewState.IdRef="TryCatch_2">
                        <TryCatch.Try>
                          <Assign DisplayName="ProcessPOInvoice_Assign_TaxAmountString_72" sap2010:WorkflowViewState.IdRef="Assign_93">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[TaxAmountString]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_AMOUNT").ToString.Split(","c)(i)]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </TryCatch.Try>
                        <TryCatch.Catches>
                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                            <ActivityAction x:TypeArguments="s:Exception">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                              </ActivityAction.Argument>
                              <Assign DisplayName="ProcessPOInvoice_Assign_TaxAmountStringEmpty_73" sap2010:WorkflowViewState.IdRef="Assign_96">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[TaxAmountString]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">
                                    <Literal x:TypeArguments="x:String" Value="" />
                                  </InArgument>
                                </Assign.Value>
                              </Assign>
                            </ActivityAction>
                          </Catch>
                        </TryCatch.Catches>
                      </TryCatch>
                      <TryCatch DisplayName="ProcessPOInvoice_TryCatch_TaxPercent_74" sap2010:WorkflowViewState.IdRef="TryCatch_1">
                        <TryCatch.Try>
                          <Sequence DisplayName="ProcessPOInvoice_Sequence_TaxPercentSequence_75" sap2010:WorkflowViewState.IdRef="Sequence_51">
                            <Assign DisplayName="ProcessPOInvoice_Assign_TaxPercentString_76" sap2010:WorkflowViewState.IdRef="Assign_94">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[TaxPercentString]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[DictOcrValues("VAT_PERCENTAGE").ToString.Split(","c)(i)]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </TryCatch.Try>
                        <TryCatch.Catches>
                          <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                            <ActivityAction x:TypeArguments="s:Exception">
                              <ActivityAction.Argument>
                                <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                              </ActivityAction.Argument>
                              <Assign DisplayName="ProcessPOInvoice_Assign_TaxPercentStringEmpty_77" sap2010:WorkflowViewState.IdRef="Assign_95">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[TaxPercentString]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">
                                    <Literal x:TypeArguments="x:String" Value="" />
                                  </InArgument>
                                </Assign.Value>
                              </Assign>
                            </ActivityAction>
                          </Catch>
                        </TryCatch.Catches>
                      </TryCatch>
                      <Assign DisplayName="ProcessPOInvoice_Assign_TaxStringConcat_78" sap2010:WorkflowViewState.IdRef="Assign_92">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[TaxString]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[TaxString + "|" + TaxPercentString + "," + TaxAmountString]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="ProcessPOInvoice_Assign_iIncrement_79" sap2010:WorkflowViewState.IdRef="Assign_98">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[i]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">[i+1]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </While>
                </Sequence>
              </If.Then>
            </If>
            <Assign DisplayName="ProcessPOInvoice_Assign_TaxStringTrim_80" sap2010:WorkflowViewState.IdRef="Assign_97">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[TaxString]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[TaxString.Trim("|"c)]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <Assign DisplayName="ProcessPOInvoice_Assign_AttributeList_81" sap2010:WorkflowViewState.IdRef="Assign_68">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[Attribute_List]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])" xml:space="preserve">[New List(Of String())() From {
    ({"", "Subtotal_Amount", DictOcrValues("SUBTOTAL").ToString, ""}),
    ({"", "PO_Number", DictOcrValues("PO_NUMBER").ToString, ""}),
    ({"", "Invoice_Type", invoiceType, ""}),
    ({"", "Invoice_Date", DictOcrValues("INVOICE_RECEIPT_DATE").ToString, ""}),
    ({"", "Additional_Attribute_1", "", ""}),
    ({"", "Updated_by_User", "N", ""}),
    ({"", "Last_Run_Time", System.DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), ""}),
    ({"", "Invoice_Number", DictOcrValues("INVOICE_RECEIPT_ID").ToString, ""}),
    ({"", "Vendor_Name", DictOcrValues("VENDOR_NAME").ToString, ""}),
    ({"", "Vendor_Address", DictOcrValues("VENDOR_ADDRESS").ToString, ""}),
    ({"", "Vendor_Phone", DictOcrValues("VENDOR_PHONE").ToString, ""}),
    ({"", "Email_Subject", emailSubject, ""}),
    ({"", "Company", company, ""}),
    ({"", "DeliveryNote_Number", DictOcrValues("DELIVERY_NOTE_DATA").ToString, ""}),
    ({"", "Email_Received_Time", emailReceivedTime, ""}),
    ({"", "Discount_Amount", DictOcrValues("Invoice_Level_Discount_AMOUNT").ToString, ""}),
    ({"", "Status", Status, ""}),
    ({"", "Failure_Count", "", ""}),
    ({"", "Comments", statusComments, ""}),
    ({"", "Vendor_ID", vendorId, ""}),
    ({"", "Shipping_Charges", DictOcrValues("SHIPPING_AND_HANDLING_AMOUNT").ToString, ""}),
    ({"", "Tax_Amount", TaxString, ""}),
    ({"", "Total_Amount", DictOcrValues("TOTAL").ToString, ""}),
    ({"", "File_Name", fileName, ""}),
    ({"", "Additional_Attribute_3", "", ""}),
    ({"", "Additional_Attribute_2", "", ""}),
    ({"", "GLCode", GLCode, ""}),
    ({"", "Division", division, ""}),
    ({"", "PID", PID, ""}),
    ({"","ProcessID",ProcessId,""}),
    ({"","APResp",APResp,""}),
    ({"","FileType",Right(fileName,3),""}),
    ({"","User_GUID",User_GUID,""})
}]</InArgument>
            </Assign.Value>
          </Assign>
          <iaw:InvokeWorkflow OutputArguments="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Attributes&quot;},{&quot;AttributeList&quot;,Attribute_List},{&quot;Operation&quot;,&quot;insert&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="ProcessPOInvoice_InvokeWorkflow_Attributes_82" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_43" ResponseCode="[attriResp]" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Attributes.xaml&quot;]" />
          <If Condition="[attriResp = 200]" DisplayName="ProcessPOInvoice_If_attriResp_83" sap2010:WorkflowViewState.IdRef="If_49">
            <If.Then>
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentName&quot;,&quot;RPA_Process_Header&quot;},{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;tenantID&quot;,tenantID},{&quot;Status_Display&quot;,Status},{&quot;RPA_Process_ID&quot;,processId},{&quot;Status&quot;,Status},{&quot;Comments&quot;,Statuscomments},{&quot;Last_Run_Time&quot;,System.DateTime.Now.ToString(&quot;yyyy-MM-ddTHH:mm:ss.fffZ&quot;)},{&quot;Process_Type&quot;,&quot;INVOICE PROCESSING&quot;},{&quot;Name&quot;,&quot;&quot;},{&quot;ERP&quot;,&quot;M3&quot;},{&quot;Variation_ID&quot;,&quot;1&quot;},{&quot;Updated_By&quot;,&quot;N&quot;},{&quot;Additional1&quot;,&quot;&quot;},{&quot;Additional2&quot;,&quot;&quot;},{&quot;failureCount&quot;,failureCount.ToString},{&quot;logFile&quot;,logFile}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_Headers_84" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_44" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_Headers.xaml&quot;]" />
            </If.Then>
          </If>
          <Sequence DisplayName="ProcessPOInvoice_Sequence_LineProcessingSequence_85" sap2010:WorkflowViewState.IdRef="Sequence_45">
            <Sequence.Variables>
              <Variable x:TypeArguments="s:String[]" Name="strArr" />
              <Variable x:TypeArguments="scg:List(s:String[])" Name="Line_List" />
              <Variable x:TypeArguments="x:Int32" Name="y" />
            </Sequence.Variables>
            <Assign DisplayName="ProcessPOInvoice_Assign_LineList_86" sap2010:WorkflowViewState.IdRef="Assign_69">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(s:String[])">[Line_List]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(s:String[])">[New List(Of String())]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[ListocrLineValues.count &gt; 0]" DisplayName="ProcessPOInvoice_If_ListocrLineValues_87" sap2010:WorkflowViewState.IdRef="If_50">
              <If.Then>
                <Sequence DisplayName="ProcessPOInvoice_Sequence_ForEachSequence_88" sap2010:WorkflowViewState.IdRef="Sequence_43">
                  <ForEach x:TypeArguments="scg:Dictionary(x:String, x:Object)" DisplayName="ProcessPOInvoice_ForEach_LineValues_89" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[ListocrLineValues]">
                    <ActivityAction x:TypeArguments="scg:Dictionary(x:String, x:Object)">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)" Name="item1" />
                      </ActivityAction.Argument>
                      <Sequence DisplayName="ProcessPOInvoice_Sequence_ItemSequence_90" sap2010:WorkflowViewState.IdRef="Sequence_42">
                        <Assign DisplayName="ProcessPOInvoice_Assign_LinesDictNew_91" sap2010:WorkflowViewState.IdRef="Assign_83">
                          <Assign.To>
                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[New Dictionary(Of String,Object)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="ProcessPOInvoice_Assign_LinesDictItem_92" sap2010:WorkflowViewState.IdRef="Assign_84">
                          <Assign.To>
                            <OutArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[LinesDict]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="scg:Dictionary(x:String, x:Object)">[item1]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="ProcessPOInvoice_Assign_strArr_93" sap2010:WorkflowViewState.IdRef="Assign_70">
                          <Assign.To>
                            <OutArgument x:TypeArguments="s:String[]">[strArr]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="s:String[]">[New String(2) {"","",""}]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="ProcessPOInvoice_Assign_strArrFirst_94" sap2010:WorkflowViewState.IdRef="Assign_71">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[strArr(0)]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">
                              <Literal x:TypeArguments="x:String" Value="" />
                            </InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="ProcessPOInvoice_Assign_ReplaceDNWithNull_96" sap2010:WorkflowViewState.IdRef="Assign_87">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[LinesDict("DELIVERY_NOTE_NUMBER")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(LinesDict("DELIVERY_NOTE_NUMBER").ToString = "" Or LinesDict("DELIVERY_NOTE_NUMBER").ToString = "''", 
   "", 
   LinesDict("DELIVERY_NOTE_NUMBER").ToString)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Assign DisplayName="ProcessPOInvoice_Assign_ReplacePOWithNull_97" sap2010:WorkflowViewState.IdRef="Assign_88">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Object">[LinesDict("PO_Number")]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Object" xml:space="preserve">[If(LinesDict("PO_Number").ToString = "" Or LinesDict("PO_Number").ToString = "''", 
   "", 
   LinesDict("PO_Number").ToString)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <If Condition="[LinesDict(&quot;DESCRIPTION&quot;).ToString = &quot;&quot; OR issueLines.contains(LinesDict(&quot;SUPPLIER_ITEM_CODE&quot;).ToString)]" DisplayName="ProcessPOInvoice_If_DescriptionOrIssueLines_98" sap2010:WorkflowViewState.IdRef="If_52">
                          <If.Then>
                            <Assign DisplayName="ProcessPOInvoice_Assign_strArrInvalid_99" sap2010:WorkflowViewState.IdRef="Assign_76">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strArr(1)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[LinesDict("SUPPLIER_ITEM_CODE").ToString+"("+LinesDict("DELIVERY_NOTE_NUMBER").ToString+","+If(LinesDict("PO_Number").ToString.contains(","),LinesDict("PO_Number").ToString+")",LinesDict("PO_Number").ToString+",)")+"||"+LinesDict("DESCRIPTION").ToString + "||" + LinesDict("QUANTITY").ToString + "||" + LinesDict("UNIT_PRICE").ToString + "||" + LinesDict("LINE_AMOUNT").ToString + "||InValid"]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Then>
                          <If.Else>
                            <Assign DisplayName="ProcessPOInvoice_Assign_strArrValid_100" sap2010:WorkflowViewState.IdRef="Assign_77">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strArr(1)]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[LinesDict("SUPPLIER_ITEM_CODE").ToString+"("+LinesDict("DELIVERY_NOTE_NUMBER").ToString+","+If(LinesDict("PO_Number").ToString.contains(","),LinesDict("PO_Number").ToString+")",LinesDict("PO_Number").ToString+",)")+"||"+LinesDict("DESCRIPTION").ToString + "||" + LinesDict("QUANTITY").ToString + "||" + LinesDict("UNIT_PRICE").ToString + "||" + LinesDict("LINE_AMOUNT").ToString + "||Valid"]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </If.Else>
                        </If>
                        <InvokeMethod DisplayName="ProcessPOInvoice_InvokeMethod_AddToLineList_101" sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(s:String[])">[Line_List]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="s:String[]">[strArr]</InArgument>
                        </InvokeMethod>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                  <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;logicalId&quot;,datalakeAPILogicalId},{&quot;documentName&quot;,&quot;RPA_Process_Lines&quot;},{&quot;AttributeList&quot;,Line_List},{&quot;Operation&quot;,&quot;insert&quot;},{&quot;Process_ID&quot;,processId},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;logFile&quot;,logFile}}]" ContinueOnError="False" DisplayName="ProcessPOInvoice_InvokeWorkflow_Lines_102" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_45" WorkflowFile="[projectPath+&quot;\SentToInvoiceProcessingResults_LineData.xaml&quot;]" />
                </Sequence>
              </If.Then>
              <If.Else>
                <Sequence DisplayName="ProcessPOInvoice_Sequence_NoLinesSequence_103" sap2010:WorkflowViewState.IdRef="Sequence_44">
                  <Assign DisplayName="ProcessPOInvoice_Assign_strArrEmpty_104" sap2010:WorkflowViewState.IdRef="Assign_73">
                    <Assign.To>
                      <OutArgument x:TypeArguments="s:String[]">[strArr]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="s:String[]">[New String(2) {"","",""}]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="ProcessPOInvoice_Assign_strArrFirstEmpty_105" sap2010:WorkflowViewState.IdRef="Assign_74">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[strArr(0)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">
                        <Literal x:TypeArguments="x:String" Value="" />
                      </InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign DisplayName="ProcessPOInvoice_Assign_strArrSecondInvalid_106" sap2010:WorkflowViewState.IdRef="Assign_75">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[strArr(1)]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">||||||||||InValid</InArgument>
                    </Assign.Value>
                  </Assign>
                  <InvokeMethod DisplayName="ProcessPOInvoice_InvokeMethod_AddEmptyToLineList_107" sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                    <InvokeMethod.TargetObject>
                      <InArgument x:TypeArguments="scg:List(s:String[])">[Line_List]</InArgument>
                    </InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="s:String[]">[strArr]</InArgument>
                  </InvokeMethod>
                </Sequence>
              </If.Else>
            </If>
          </Sequence>
        </Sequence>
      </If.Then>
    </If>
    <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;status&quot;,status},{&quot;statusComments&quot;,statusComments},{&quot;distributionType&quot;,distributionType},{&quot;userIdentifier&quot;,userIdentifier},{&quot;emailSubject&quot;,emailSubject},{&quot;emailReceivedTime&quot;,emailReceivedTime},{&quot;tenantID&quot;,tenantID},{&quot;invoiceNumber&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString},{&quot;poNumber&quot;,DictOcrValues(&quot;PO_NUMBER&quot;).ToString},{&quot;subTotal&quot;,DictOcrValues(&quot;SUBTOTAL&quot;).ToString},{&quot;total&quot;,DictOcrValues(&quot;TOTAL&quot;).ToString},{&quot;drillbackLink&quot;,&quot;LogicalId=lid://infor.m3.m3&amp;program=APS450&amp;fieldNames=W2OBKV,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;,W3OBKV,,W1OBKV,&quot;+ division+&quot;&amp;includeStartPanel=True&amp;source=MForms&amp;requirePanel=True&amp;sortingOrder=2&amp;view=STD02-01&amp;tableName=FAPIBH&amp;keys=E5CONO,&quot;+company+&quot;,E5DIVI,&quot;+division+&quot;,E5INBN,+&amp;parameters=XXSINO,&quot;+DictOcrValues(&quot;INVOICE_RECEIPT_ID&quot;).ToString+&quot;&amp;startpanel=B&quot;},{&quot;invoiceDate&quot;,DictOcrValues(&quot;INVOICE_RECEIPT_DATE&quot;).ToString},{&quot;tax&quot;,DictOcrValues(&quot;VAT_AMOUNT&quot;).ToString},{&quot;charges&quot;,DictOcrValues(&quot;SHIPPING_AND_HANDLING_AMOUNT&quot;).ToString},{&quot;discount&quot;,DictOcrValues(&quot;Invoice_Level_Discount_AMOUNT&quot;).ToString},{&quot;deliveryNote&quot;,DictOcrValues(&quot;DELIVERY_NOTE_DATA&quot;).ToString} ,{&quot;miscValues&quot;,miscValues},{&quot;APResp&quot;,APResp}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_SendNotification_108" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_47" WorkflowFile="[projectPath+&quot;\SendNotification.xaml&quot;]" />
    <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,documentPath},{&quot;configurationFolder&quot;,configurationFolder},{&quot;status&quot;,status}}]" ContinueOnError="True" DisplayName="ProcessPOInvoice_InvokeWorkflow_MoveFile_109" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_48" WorkflowFile="[projectpath+&quot;\&quot;+&quot;MoveFileToSuccessFailureFolder.xaml&quot;]" />
    <sads:DebugSymbol.Symbol>d1xDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXFByb2Nlc3NQT0ludm9pY2UueGFtbJYCbAOkBg4CAQF8BYMBDgMBvAOEAQWLAQ4DAbgDjAEFkQEOAwGVA5IBBasDCgMB5AGsAwWsA5ACAwHeAa0DBaAGCgIBCqEGBaEG8Q4CAQaiBgWiBuADAgECgQEwgQExAwG/A34xfksDAb0DiQE6iQFPAwG7A4YBO4YBSQMBuQOMAWeMAXYDAZYDjQEHjQGTCQMBsAOOAQeOAckJAwGoA48BB48B/AQDAaADkAEHkAGmBwMBmAOSAROSATEDAeUBlAEJlQMUAwHwAZgDCakDFAMB5wGsA7MBrAP6AQMB4QGsA4ICrAONAgMB3wGtAxOtA0gCAQuvAwmeBhQCAQ6hBlahBqANAgEJoQa8DqEG7g4CAQeiBlaiBvwBAgEFogaQA6IG3QMCAQONAbQIjQHNCAMBtgONAYcIjQGmCAMBtAONATqNAe4GAwGzA40B2wiNAZAJAwGxA44B5giOAf8IAwGuA44BuQiOAdgIAwGsA44BN44BowcDAasDjgGNCY4BxgkDAakDjwGcBI8BtQQDAaYDjwHvA48BjgQDAaQDjwE3jwHZAgMBowOPAcMEjwH5BAMBoQOQAb4GkAHXBgMBngOQAZEGkAGwBgMBnAOQATWQAf0EAwGbA5AB5QaQAaMHAwGZA5UBC5wBFAMBkAOdAQukARQDAYsDpQELrAEUAwGGA60BC7QBFAMBggO1AQvAARADAfoCwQELzAEQAwHyAs0BC9gBEAMB6gLZAQviARQDAeYC4wELlQIQAwHBApYCC6ECEAMBuQKiAgupAhQDAbQCqgILrgIQAwGtAq8CC/ACEAMBiALxAguUAxADAfEBmQMLoAMUAwHsAaEDC6gDFAMB6AG0Awu7AxQDAdoBvAMLwwMUAwHUAcQDC8QDowQDAc4BxQMLzAMUAwHJAc0DC9QDFAMBxAHVAwvYBBYDAYYB2QQLggUUAwGCAYMFC4MFkwUCAXyEBQuIBRACAXWJBQudBhYCAQ+aATaaAWwDAZMDlwE3lwE/AwGRA6IBNqIBcwMBjgOfATefAUcDAYwDqgE2qgFwAwGJA6cBN6cBQwMBhwOyAUCyAYUBAwGFA68BQa8BTwMBgwO1ARm1ATQDAfsCtwEPvgEYAwH9AsEBGcEBMwMB8wLDAQ/KARgDAfUCzQEZzQE0AwHrAs8BD9YBGAMB7QLfARHfAUADAekC2wE32wE/AwHnAuMBGeMBfgMBwgLlAQ+TAhoDAcUClgIZlgI2AwG6ApgCD58CGAMBvAKnAjanAlIDAbcCpAI3pAJDAwG1AqoCGaoCLwMBrgKsAg+sArgCAwGwAq8CGa8CNgMBiQKxAg/uAhQDAYsC8QIZ8QI5AwHyAfMCD5IDGgMB9AGeAzaeAz0DAe8BmwM3mwM/AwHtAaYDNqYDaAMB6wGjAzejA0cDAekBuQM2uQN3AwHdAbYDN7YDQQMB2wHBAzbBA54BAwHXAb4DN74DQgMB1QHEA8gDxAPhAwMB0gHEA0HEA7ECAwHRAcQD7wPEA6AEAwHPAcoDNsoDcAMBzAHHAzfHA0MDAcoB0gM20gNjAwHHAc8DN88DPAMBxQHWAw3PBBIDAYwB0AQN1wQWAwGHAdsEQ9sEUwMBgwGDBaoEgwW3BAMBgAGDBUSDBZQDAgF/gwXFBIMFkAUCAX2EBRmEBSwCAXaGBQ+GBeYIAgF4jwUNlgUWAgFxlwUNnAYSAgEQvAE6vAFyAwGAA7kBO7kBRQMB/gLIATrIAXEDAfgCxQE7xQFEAwH2AtQBOtQBcgMB8ALRATvRAUUDAe4C7AER8wEaAwHhAvQBEfsBGgMB3AL8ARH8AfQEAwHUAv0BEZICFgMBxgKdAjqdAmEDAb8CmgI7mgJHAwG9AqwC7gGsArUCAwGxArECHbECNAMBjAKzAhPsAh4DAY4C+QIRgAMaAwGDAoEDEYgDGgMB/gGJAxGQAxoDAfkBkQMRkQPIBgMB9QHWAxvWA2MDAY0B2AMRzQQcAwGPAdUEONUETgMBigHSBDnSBEQDAYgBhgVghgWJBwIBe4YFmwiGBeMIAgF5lAVElAVbAgF0kQVFkQVQAgFylwUblwU9AgERmQUR9gUcAgEl+QURmgYcAgET8QE88QFxAwHkAu4BPe4BRAMB4gL5ATz5AXIDAd8C9gE99gFFAwHdAvwBqQT8AbsEAwHaAvwBiQT8AZsEAwHYAvwBL/wB9QIDAdcC/AHJBPwB8QQDAdUC/QEf/QE3AwHHAv8BFZACIAMByQK0AhXdAhoDAZcC3gIV6wIgAwGPAv4CPP4CeQMBhgL7Aj37AkwDAYQChgM8hgNxAwGBAoMDPYMDRAMB/wGOAzyOA3IDAfwBiwM9iwNFAwH6AZEDYpEDjAUDAfgBkQOaBpEDxQYDAfYB3wMT5gMcAwHAAecDE+4DHAMBvAHvAxP4AxwDAbgB+QMTzAQbAwGQAZoFE/QFHQIBKvUFE/UFiAUCASb6BROBBhwCASGCBhOLBhwCAR2MBhOTBhwCARmUBhOZBiICARSAAheHAiADAc8CiAIXjwIgAwHKArQCI7QCSQMBmAK2AhnHAiQDAaQCygIZ2wIkAwGaAt8CF+oCHAMBkALkAz3kA3UDAcMB4QM+4QNLAwHBAewDPewDPgMBvwHpAz7pA0EDAb0B9QMZ9QNIAwG7AfEDP/EDSgMBuQH6AxXLBCADAZQB+QN++QOUAQMBkQGaBbsBmgXQAQIBb58FF/IFIgIBK/UFZPUFqgMCASn1BbwE9QWFBQIBJ/8FQP8FWgIBJPwFQfwFSQIBIogGGYgGSAIBIIQGP4QGSgIBHpEGPpEGTwIBHI4GP44GSgIBGpYGSpYGVQIBF5gGPpgGRgIBFYUCQoUCawMB0gKCAkOCAksDAdACjQJCjQJuAwHNAooCQ4oCTgMBywK3Ahu+AiQDAakCvwIbxgIkAwGlAssCG9ICJAMBoALTAhvaAiQDAZsC3wIl3wJzAwGRAuECG+gCJAMBkwL7AxeZBCIDAa0BmgQXugQiAwGhAbsEF8IEIAMBmgHDBBfKBCADAZUBoAUZpwUiAgFrqAUZrwUiAgFmsAUZtwUiAgFiuAUZwQUiAgFewgUZywUiAgFXzAUZ1QUiAgFQ1gUZ6wUeAgEx7AUZ8QUoAgEsvAJFvAJGAwGsArkCRrkCVAMBqgLEAkbEAlUDAagCwQJHwQJTAwGmAtACRdACRgMBowLNAkbNAlQDAaEC2AJG2AJOAwGeAtUCR9UCUwMBnALmAkbmAlcDAZYC4wJH4wJPAwGUAv0DG4QEJAMBsgGMBB+VBCgDAa4BnAQbpQQmAwGmAa0EH7YEKAMBogHABELABH4DAZ0BvQRDvQROAwGbAcgEQcgERgMBmAHFBELFBEUDAZYBpQVepQWAAQIBbqIFX6IFagIBbK0FXq0FZQIBaaoFX6oFagIBZ7UFRrUFYAIBZbIFR7IFTwIBY74FH74FTgIBYboFRboFUAIBX8QFRcQFaAIBWM4FRc4FXQIBUdYFJ9YFsAECATLYBR3fBSYCAUPiBR3pBSYCATbuBVDuBVsCAS/wBUTwBUwCAS2CBEaCBHsDAbUB/wNH/wNYAwGzAZIEJZIEVAMBsQGOBEuOBFwDAa8BnQQdpAQmAwGnAbMEJbMEVAMBpQGvBEuvBF0DAaMB3QVI3QXSAwIBRtoFSdoFVAIBROcFSOcF0AMCATnkBUnkBVQCATeiBEiiBIEBAwGqAZ8ESZ8EWwMBqAE=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1136,60" />
      <sap2010:ViewStateData Id="Assign_78" sap:VirtualizedContainerService.HintSize="1136,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_23" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_37" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_21" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_22" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_1" sap:VirtualizedContainerService.HintSize="1136,204">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="825,208" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="825,208" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="825,208" />
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="Assign_102" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_103" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_50" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_104" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_53" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_56" sap:VirtualizedContainerService.HintSize="464,432" />
      <sap2010:ViewStateData Id="Sequence_52" sap:VirtualizedContainerService.HintSize="486,818">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_57" sap:VirtualizedContainerService.HintSize="825,966" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="825,208" />
      <sap2010:ViewStateData Id="Assign_22" sap:VirtualizedContainerService.HintSize="825,60" />
      <sap2010:ViewStateData Id="MessageBox_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_10" sap:VirtualizedContainerService.HintSize="825,208" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_39" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_30" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_38" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_31" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_20" sap:VirtualizedContainerService.HintSize="553,432" />
      <sap2010:ViewStateData Id="Assign_48" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="464,208" />
      <sap2010:ViewStateData Id="Sequence_29" sap:VirtualizedContainerService.HintSize="553,332">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_17" sap:VirtualizedContainerService.HintSize="575,928">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_19" sap:VirtualizedContainerService.HintSize="700,1076" />
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="825,1224" />
      <sap2010:ViewStateData Id="Assign_45" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_52" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_53" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_32" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_34" sap:VirtualizedContainerService.HintSize="264,446">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="825,594">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="847,4828">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_13" sap:VirtualizedContainerService.HintSize="264,284">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="1136,4976">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="1136,22" />
      <sap2010:ViewStateData Id="Assign_65" sap:VirtualizedContainerService.HintSize="894,60" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="894,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_42" sap:VirtualizedContainerService.HintSize="894,22" />
      <sap2010:ViewStateData Id="Assign_101" sap:VirtualizedContainerService.HintSize="894,60" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="894,60" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="400,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="414,304.666666666667" />
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_51" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="400,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="414,304.666666666667" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="414,62" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="414,62" />
      <sap2010:ViewStateData Id="Sequence_50" sap:VirtualizedContainerService.HintSize="436,977.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="464,1141.33333333333" />
      <sap2010:ViewStateData Id="Sequence_49" sap:VirtualizedContainerService.HintSize="486,1571.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_55" sap:VirtualizedContainerService.HintSize="612,1725.33333333333" />
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Sequence_48" sap:VirtualizedContainerService.HintSize="894,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="894,64" />
      <sap2010:ViewStateData Id="InvokeWorkflow_43" sap:VirtualizedContainerService.HintSize="894,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_44" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_49" sap:VirtualizedContainerService.HintSize="894,208" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="872,60" />
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="509,60" />
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="509,64" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="509,64" />
      <sap2010:ViewStateData Id="Assign_76" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_77" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="If_52" sap:VirtualizedContainerService.HintSize="509,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="509,128" />
      <sap2010:ViewStateData Id="Sequence_42" sap:VirtualizedContainerService.HintSize="531,1108">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="561,1256">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_45" sap:VirtualizedContainerService.HintSize="561,22" />
      <sap2010:ViewStateData Id="Sequence_43" sap:VirtualizedContainerService.HintSize="583,1442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_73" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_74" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_75" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Sequence_44" sap:VirtualizedContainerService.HintSize="264,552">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_50" sap:VirtualizedContainerService.HintSize="872,1590">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_45" sap:VirtualizedContainerService.HintSize="894,1814">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_46" sap:VirtualizedContainerService.HintSize="916,2905">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_51" sap:VirtualizedContainerService.HintSize="1136,3053">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_47" sap:VirtualizedContainerService.HintSize="1136,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_48" sap:VirtualizedContainerService.HintSize="1136,22" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="1158,8823">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1198,8903" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>