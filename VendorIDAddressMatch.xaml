﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="VendorName" Type="InArgument(x:String)" />
    <x:Property Name="VendorAddress" Type="InArgument(x:String)" />
    <x:Property Name="VendorPhone" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="vendorID" Type="OutArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Newtonsoft.Json</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="VendorIDAddressMatch_Sequence_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="vendorTextInput" />
      <Variable x:TypeArguments="njl:JToken" Name="vendorjsoninput" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="vendorresponseobj" />
      <Variable x:TypeArguments="s:String[]" Name="vendorResp1" />
      <Variable x:TypeArguments="x:String" Name="vendDetails" />
      <Variable x:TypeArguments="x:String" Name="strIBAN" />
      <Variable x:TypeArguments="x:String" Name="strVATCode" />
    </Sequence.Variables>
    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
      <TryCatch.Try>
        <Assign DisplayName="VendorIDAddressMatch_Assign_strIBAN_2" sap2010:WorkflowViewState.IdRef="Assign_3">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[strIBAN]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[DictOcrValues("IBAN").ToString]</InArgument>
          </Assign.Value>
        </Assign>
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
            <Assign DisplayName="VendorIDAddressMatch_Assign_strIBAN_2" sap2010:WorkflowViewState.IdRef="Assign_5">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[strIBAN]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_2">
      <TryCatch.Try>
        <Assign DisplayName="VendorIDAddressMatch_Assign_strVATCode_3" sap2010:WorkflowViewState.IdRef="Assign_4">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[strVATCode]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[DictOcrValues("UNIQUE_REGISTRATION_CODE").ToString]</InArgument>
          </Assign.Value>
        </Assign>
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
            <Assign DisplayName="VendorIDAddressMatch_Assign_strVATCode_3" sap2010:WorkflowViewState.IdRef="Assign_6">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[strVATCode]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">
                  <Literal x:TypeArguments="x:String" Value="" />
                </InArgument>
              </Assign.Value>
            </Assign>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VendorIDAddressMatch_TemplateApply_vendorTextInput_4" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{'data':[['vendorid','{{%VendorName%}}','{{%VendorAddress%}}','{{%VendorPhone%}}','{{%strIBAN%}}','{{%strVATCode%}}']]}" Text="[vendorTextInput]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>VendorName</x:String>
            <x:String>VendorAddress</x:String>
            <x:String>VendorPhone</x:String>
            <x:String>strIBAN</x:String>
            <x:String>strVATCode</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="8">
            <x:String>VendorName</x:String>
            <x:String>VendorAddress</x:String>
            <x:String>VendorPhone</x:String>
            <x:String>strIBAN</x:String>
            <x:String>strVATCode</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VendorIDAddressMatch_DeserializeJSON_vendorjsoninput_5" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[vendorjsoninput]" JTokenString="[vendorTextInput]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VendorIDAddressMatch_IONAPIRequestWizard_vendorresponseobj_6" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" PostData="[vendorjsoninput.tostring]" Response="[vendorresponseobj]" Url="[tenantID + &quot;COLEMANAI/aiplatform/v1/endpoints/M3_IP_AI_Model/prediction&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
    </iai:IONAPIRequestWizard>
    <Assign DisplayName="VendorIDAddressMatch_Assign_vendorID_7" sap2010:WorkflowViewState.IdRef="Assign_1">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[vendorresponseobj.ReadAsJson(0)(0).ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[vendorID = &quot;No_Similarity&quot;]" DisplayName="VendorIDAddressMatch_If_vendorIDNoSimilarity_8" sap2010:WorkflowViewState.IdRef="If_1">
      <If.Then>
        <Assign DisplayName="VendorIDAddressMatch_Assign_vendorIDEmpty_9" sap2010:WorkflowViewState.IdRef="Assign_2">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">
              <Literal x:TypeArguments="x:String" Value="" />
            </InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>d15DOlxVc2Vyc1xzaW5hbXB1ZGlcQXBwRGF0YVxMb2NhbFxJbmZvclJQQVxNM0ludm9pY2VQcm9jZXNzaW5nR2VuQUlWM1xWZW5kb3JJREFkZHJlc3NNYXRjaC54YW1sIUYDxgEOAgEBUAVuEAIBJm8FjQEQAgEcjgEFoQEaAgEYogEFogGDAgIBE6MBBa4BHwIBDa8BBbYBDgIBCbcBBcQBCgIBAlIJWRICASthDWoWAgEncQl4EgIBIYABDYkBFgIBHY4BxQKOAdgCAgEajgHEAY4BvwICARmiAe0BogGAAgIBFqIBzAGiAd8BAgEUowHWAqMB8gICARKjAfwCowGRAwIBEKMBlgOjAewDAgEOtAEwtAFdAgEMsQExsQE7AgEKtwETtwE7AgEDuQEJwgESAgEFVzRXVAIBLlQ1VD4CASxnE2dCAgEqYzljQgIBKHY0dmgCASRzNXNBAgEihgEThgFCAgEgggE5ggFFAgEevwEPvwE+AgEIuwE1uwE/AgEG</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404.666666666667,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="464,514.666666666667" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404.666666666667,132.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="464,514.666666666667" />
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="486,1737.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="526,2017.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="ShouldExpandAll">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>