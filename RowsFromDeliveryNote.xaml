﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="M3TotalTableRows" Type="OutArgument(scg:List(s:String[]))" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="VendorID" Type="OutArgument(x:String)" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="DictOcrValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.Activities.Workflow</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.IO</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.Activities.Workflow</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="RowsFromDeliveryNote_Sequence_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_28">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:Int32" Name="vendorResponseCode" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="vendorResponseDictionary" />
    </Sequence.Variables>
    <If Condition="True" DisplayName="RowsFromDeliveryNote_If_True_2" sap2010:WorkflowViewState.IdRef="If_18">
      <If.Then>
        <Sequence DisplayName="RowsFromDeliveryNote_Sequence_ProcessingSequence_3" sap2010:WorkflowViewState.IdRef="Sequence_27">
          <Sequence.Variables>
            <Variable x:TypeArguments="x:Boolean" Name="dnExists" />
            <Variable x:TypeArguments="x:String" Name="dn" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="deliveryNumbers" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="vendorList" />
          </Sequence.Variables>
          <Assign DisplayName="RowsFromDeliveryNote_Assign_deliveryNumbers_4" sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="RowsFromDeliveryNote_Assign_deliveryNumbers_5" sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[deliveryNumbers]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[DictOcrValues("DELIVERY_NOTE_DATA").Tostring.split(","c).ToList]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="RowsFromDeliveryNote_Assign_dnExists_6" sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Boolean">True</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="RowsFromDeliveryNote_Assign_M3TotalTableRows_7" sap2010:WorkflowViewState.IdRef="Assign_4">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="RowsFromDeliveryNote_Assign_dn_8" sap2010:WorkflowViewState.IdRef="Assign_5">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">
                <Literal x:TypeArguments="x:String" Value="" />
              </InArgument>
            </Assign.Value>
          </Assign>
          <ForEach x:TypeArguments="x:String" DisplayName="RowsFromDeliveryNote_ForEach_deliveryNumbers_9" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[deliveryNumbers]">
            <ActivityAction x:TypeArguments="x:String">
              <ActivityAction.Argument>
                <DelegateInArgument x:TypeArguments="x:String" Name="item0" />
              </ActivityAction.Argument>
              <Sequence DisplayName="RowsFromDeliveryNote_Sequence_DeliverySequence_10" sap2010:WorkflowViewState.IdRef="Sequence_79">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="tolamount" />
                  <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="linesOcrWorkflowOutput" />
                  <Variable x:TypeArguments="x:Int32" Name="linesOcrWorkflowStatus" />
                  <Variable x:TypeArguments="x:String" Name="req2" />
                </Sequence.Variables>
                <Assign DisplayName="RowsFromDeliveryNote_Assign_req2_11" sap2010:WorkflowViewState.IdRef="Assign_79">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2SUDO = " + item0.trim()+ " and F2IMST != 9 "]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="RowsFromDeliveryNote_InvokeWorkflow_linesOcrWorkflowOutput_12" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_12" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                <If Condition="[linesOcrWorkflowStatus = 200]" DisplayName="RowsFromDeliveryNote_If_linesOcrWorkflowStatus_13" sap2010:WorkflowViewState.IdRef="If_48">
                  <If.Then>
                    <Sequence DisplayName="RowsFromDeliveryNote_Sequence_SuccessSequence_14" sap2010:WorkflowViewState.IdRef="Sequence_77">
                      <Sequence.Variables>
                        <Variable x:TypeArguments="scg:List(s:String[])" Name="M3TotalTableRows1" />
                        <Variable x:TypeArguments="x:Boolean" Name="DNinM3exists" />
                      </Sequence.Variables>
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_M3TotalTableRows1_15" sap2010:WorkflowViewState.IdRef="Assign_80">
                        <Assign.To>
                          <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_DNinM3exists_16" sap2010:WorkflowViewState.IdRef="Assign_81">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[DNinM3exists]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">[CType(linesOcrWorkflowOutput("DNinM3exists"), Boolean)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorID_17" sap2010:WorkflowViewState.IdRef="Assign_82">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("VendorID"), String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[M3TotalTableRows1.Count &gt; 0]" DisplayName="RowsFromDeliveryNote_If_M3TotalTableRows1_18" sap2010:WorkflowViewState.IdRef="If_47">
                        <If.Then>
                          <Sequence DisplayName="RowsFromDeliveryNote_Sequence_RowsFoundSequence_19" sap2010:WorkflowViewState.IdRef="Sequence_65">
                            <If Condition="[dnExists]" DisplayName="RowsFromDeliveryNote_If_dnExists_20" sap2010:WorkflowViewState.IdRef="If_39">
                              <If.Then>
                                <Sequence DisplayName="RowsFromDeliveryNote_Sequence_AddRowsSequence_21" sap2010:WorkflowViewState.IdRef="Sequence_64">
                                  <ForEach x:TypeArguments="s:String[]" DisplayName="RowsFromDeliveryNote_ForEach_M3TotalTableRows1_22" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[M3TotalTableRows1]">
                                    <ActivityAction x:TypeArguments="s:String[]">
                                      <ActivityAction.Argument>
                                        <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                      </ActivityAction.Argument>
                                      <Sequence DisplayName="RowsFromDeliveryNote_Sequence_AddItemSequence_23" sap2010:WorkflowViewState.IdRef="Sequence_63">
                                        <InvokeMethod DisplayName="RowsFromDeliveryNote_InvokeMethod_Add_24" sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                                          <InvokeMethod.TargetObject>
                                            <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                          </InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                        </InvokeMethod>
                                      </Sequence>
                                    </ActivityAction>
                                  </ForEach>
                                </Sequence>
                              </If.Then>
                            </If>
                          </Sequence>
                        </If.Then>
                        <If.Else>
                          <Sequence DisplayName="RowsFromDeliveryNote_Sequence_NoRowsSequence_25" sap2010:WorkflowViewState.IdRef="Sequence_76">
                            <Assign DisplayName="RowsFromDeliveryNote_Assign_dn_26" sap2010:WorkflowViewState.IdRef="Assign_83">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[dn]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[dn + item0.trim + ", "]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <If Condition="[DNinM3exists]" DisplayName="RowsFromDeliveryNote_If_DNinM3exists_27" sap2010:WorkflowViewState.IdRef="If_46">
                              <If.Then>
                                <Sequence DisplayName="RowsFromDeliveryNote_Sequence_AlreadyInvoicedSequence_28" sap2010:WorkflowViewState.IdRef="Sequence_66">
                                  <Assign DisplayName="RowsFromDeliveryNote_Assign_VendorID_29" sap2010:WorkflowViewState.IdRef="Assign_84">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[VendorID]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("VendorID"), String)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="RowsFromDeliveryNote_Assign_commentStatus_30" sap2010:WorkflowViewState.IdRef="Assign_85">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["Lines already invoiced for delivery notes "]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign DisplayName="RowsFromDeliveryNote_Assign_Status_31" sap2010:WorkflowViewState.IdRef="Assign_86">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">Failure</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                </Sequence>
                              </If.Then>
                              <If.Else>
                                <Sequence DisplayName="RowsFromDeliveryNote_Sequence_NotInvoicedSequence_32" sap2010:WorkflowViewState.IdRef="Sequence_75">
                                  <If Condition="[deliveryNumbers.Count = 1]" DisplayName="RowsFromDeliveryNote_If_deliveryNumbers_33" sap2010:WorkflowViewState.IdRef="If_45">
                                    <If.Then>
                                      <Sequence DisplayName="RowsFromDeliveryNote_Sequence_SingleDeliverySequence_34" sap2010:WorkflowViewState.IdRef="Sequence_73">
                                        <Assign DisplayName="RowsFromDeliveryNote_Assign_req2_35" sap2010:WorkflowViewState.IdRef="Assign_87">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[req2]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String" xml:space="preserve">["F2PNLI,F2REPN,F2SCOC,F2RPQA,F2TRDT,F2ITNO,F2CONO,F2CUCD,F2DIVI,F2SUNO,F2IVQA,F2PUNO,F2SUDO,F2RCAC,F2RPQT,F2SCOP,F2SERA,F2IVQT,F2ICAC  from FGRECL where F2PUNO = " + DictOcrValues("PO_NUMBER").Tostring+ " and F2DIVI = "+division + " and F2IMST != 9 "]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;logfile&quot;,logfile},{&quot;miscValues&quot;,miscValues},{&quot;req2&quot;,req2}}]" ContinueOnError="True" DisplayName="RowsFromDeliveryNote_InvokeWorkflow_linesOcrWorkflowOutput_36" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_13" OutputArguments="[linesOcrWorkflowOutput]" ResponseCode="[linesOcrWorkflowStatus]" WorkflowFile="[projectPath+&quot;\LinesExtractionWithPO.xaml&quot;]" />
                                        <If Condition="[linesOcrWorkflowStatus = 200]" DisplayName="RowsFromDeliveryNote_If_linesOcrWorkflowStatus_37" sap2010:WorkflowViewState.IdRef="If_44">
                                          <If.Then>
                                            <Sequence DisplayName="RowsFromDeliveryNote_Sequence_POExtractionSequence_38" sap2010:WorkflowViewState.IdRef="Sequence_72">
                                              <Assign DisplayName="RowsFromDeliveryNote_Assign_M3TotalTableRows_39" sap2010:WorkflowViewState.IdRef="Assign_88">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[new list(OF string())]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign DisplayName="RowsFromDeliveryNote_Assign_M3TotalTableRows1_40" sap2010:WorkflowViewState.IdRef="Assign_89">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows1]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[CType(linesOcrWorkflowOutput("M3TotalTableRows"), List(Of String()))]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorID_41" sap2010:WorkflowViewState.IdRef="Assign_90">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("VendorID"), String)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                              <If Condition="[M3TotalTableRows1.Count &gt; 0]" DisplayName="RowsFromDeliveryNote_If_M3TotalTableRows1_42" sap2010:WorkflowViewState.IdRef="If_43">
                                                <If.Then>
                                                  <If Condition="[dnExists]" DisplayName="RowsFromDeliveryNote_If_dnExists_43" sap2010:WorkflowViewState.IdRef="If_42">
                                                    <If.Then>
                                                      <Sequence DisplayName="RowsFromDeliveryNote_Sequence_ExtractedSequence_44" sap2010:WorkflowViewState.IdRef="Sequence_68">
                                                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="RowsFromDeliveryNote_Append_Line_logfile_45" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[&quot;Receipt lines for the delivery note number &quot;  + &quot; is extracted.&quot;]" Source="[logfile]" />
                                                        <ForEach x:TypeArguments="s:String[]" DisplayName="RowsFromDeliveryNote_ForEach_M3TotalTableRows1_46" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[M3TotalTableRows1]">
                                                          <ActivityAction x:TypeArguments="s:String[]">
                                                            <ActivityAction.Argument>
                                                              <DelegateInArgument x:TypeArguments="s:String[]" Name="item" />
                                                            </ActivityAction.Argument>
                                                            <Sequence DisplayName="RowsFromDeliveryNote_Sequence_AddRowSequence_47" sap2010:WorkflowViewState.IdRef="Sequence_67">
                                                              <InvokeMethod DisplayName="RowsFromDeliveryNote_InvokeMethod_Add_48" sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                                                <InvokeMethod.TargetObject>
                                                                  <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</InArgument>
                                                                </InvokeMethod.TargetObject>
                                                                <InArgument x:TypeArguments="s:String[]">[item]</InArgument>
                                                              </InvokeMethod>
                                                            </Sequence>
                                                          </ActivityAction>
                                                        </ForEach>
                                                      </Sequence>
                                                    </If.Then>
                                                    <If.Else>
                                                      <Sequence DisplayName="RowsFromDeliveryNote_Sequence_VendorMatchSequence_49" sap2010:WorkflowViewState.IdRef="Sequence_71">
                                                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;VendorName&quot;,DictOcrValues(&quot;VENDOR_NAME&quot;).ToString},{&quot;VendorAddress&quot;,DictOcrValues(&quot;VENDOR_ADDRESS&quot;).Tostring},{&quot;VendorPhone&quot;,DictOcrValues(&quot;VENDOR_PHONE&quot;).Tostring},{&quot;DictOcrValues&quot;,DictOcrValues}}]" ContinueOnError="True" DisplayName="RowsFromDeliveryNote_InvokeWorkflow_vendorResponseDictionary_50" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_14" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\vendorIDAddressMatch.xaml&quot;]" />
                                                        <If Condition="[vendorResponseCode = 200]" DisplayName="RowsFromDeliveryNote_If_vendorResponseCode_51" sap2010:WorkflowViewState.IdRef="If_40">
                                                          <If.Then>
                                                            <Sequence DisplayName="RowsFromDeliveryNote_Sequence_VendorFoundSequence_52" sap2010:WorkflowViewState.IdRef="Sequence_69">
                                                              <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorId_53" sap2010:WorkflowViewState.IdRef="Assign_91">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("vendorID"), String)]</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </Sequence>
                                                          </If.Then>
                                                        </If>
                                                        <If Condition="[vendorId &lt;&gt; &quot;&quot;]" DisplayName="RowsFromDeliveryNote_If_vendorId_54" sap2010:WorkflowViewState.IdRef="If_41">
                                                          <If.Else>
                                                            <Sequence DisplayName="RowsFromDeliveryNote_Sequence_MissingInfoSequence_55" sap2010:WorkflowViewState.IdRef="Sequence_70">
                                                              <Assign DisplayName="RowsFromDeliveryNote_Assign_commentStatus_56" sap2010:WorkflowViewState.IdRef="Assign_92">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">Missing required Information</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                              <Assign DisplayName="RowsFromDeliveryNote_Assign_Status_57" sap2010:WorkflowViewState.IdRef="Assign_93">
                                                                <Assign.To>
                                                                  <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                                                </Assign.To>
                                                                <Assign.Value>
                                                                  <InArgument x:TypeArguments="x:String">MISSINGINFORMATION</InArgument>
                                                                </Assign.Value>
                                                              </Assign>
                                                            </Sequence>
                                                          </If.Else>
                                                        </If>
                                                      </Sequence>
                                                    </If.Else>
                                                  </If>
                                                </If.Then>
                                              </If>
                                            </Sequence>
                                          </If.Then>
                                        </If>
                                      </Sequence>
                                    </If.Then>
                                    <If.Else>
                                      <Sequence DisplayName="RowsFromDeliveryNote_Sequence_NoReceiptsSequence_58" sap2010:WorkflowViewState.IdRef="Sequence_74">
                                        <Assign DisplayName="RowsFromDeliveryNote_Assign_commentStatus_59" sap2010:WorkflowViewState.IdRef="Assign_94">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">["No receipts available for the given delivery Note: " + dn.Substring(0,dn.Length-2)]</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                        <Assign DisplayName="RowsFromDeliveryNote_Assign_Status_60" sap2010:WorkflowViewState.IdRef="Assign_95">
                                          <Assign.To>
                                            <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                                          </Assign.To>
                                          <Assign.Value>
                                            <InArgument x:TypeArguments="x:String">PONOTRECEIVED</InArgument>
                                          </Assign.Value>
                                        </Assign>
                                      </Sequence>
                                    </If.Else>
                                  </If>
                                </Sequence>
                              </If.Else>
                            </If>
                            <Assign DisplayName="RowsFromDeliveryNote_Assign_dnExists_61" sap2010:WorkflowViewState.IdRef="Assign_96">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </If.Else>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <Sequence DisplayName="RowsFromDeliveryNote_Sequence_ErrorSequence_62" sap2010:WorkflowViewState.IdRef="Sequence_78">
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_commentStatus_63" sap2010:WorkflowViewState.IdRef="Assign_97">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("commentStatus"), String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_Status_64" sap2010:WorkflowViewState.IdRef="Assign_98">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(linesOcrWorkflowOutput("Status"), String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_dnExists_65" sap2010:WorkflowViewState.IdRef="Assign_99">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[dnExists]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </ActivityAction>
          </ForEach>
          <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorList_66" sap2010:WorkflowViewState.IdRef="Assign_27">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(x:String)">[vendorList]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(x:String)">[M3TotalTableRows.Where(Function(arr) arr.Length &gt; 1). Select(Function(arr) arr(7)).Distinct().ToList()]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[vendorList.Count &gt; 1 AND deliveryNumbers.Count &lt;&gt; 1]" DisplayName="RowsFromDeliveryNote_If_vendorList_67" sap2010:WorkflowViewState.IdRef="If_14">
            <If.Then>
              <Sequence DisplayName="RowsFromDeliveryNote_Sequence_MultipleVendorsSequence_68" sap2010:WorkflowViewState.IdRef="Sequence_22">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="vendorStr" />
                </Sequence.Variables>
                <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorStr_69" sap2010:WorkflowViewState.IdRef="Assign_28">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorStr]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">
                      <Literal x:TypeArguments="x:String" Value="" />
                    </InArgument>
                  </Assign.Value>
                </Assign>
                <ForEach x:TypeArguments="x:String" DisplayName="RowsFromDeliveryNote_ForEach_vendorList_70" sap2010:WorkflowViewState.IdRef="ForEach`1_4" Values="[vendorList]">
                  <ActivityAction x:TypeArguments="x:String">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                    </ActivityAction.Argument>
                    <Sequence DisplayName="RowsFromDeliveryNote_Sequence_VendorStringSequence_71" sap2010:WorkflowViewState.IdRef="Sequence_19">
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorStr_72" sap2010:WorkflowViewState.IdRef="Assign_29">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorStr]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[vendorStr + item + ";"]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </ActivityAction>
                </ForEach>
                <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorStr_73" sap2010:WorkflowViewState.IdRef="Assign_30">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorStr]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[vendorStr.SubString(0,vendorStr.length-1)]</InArgument>
                  </Assign.Value>
                </Assign>
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;vendorName&quot;,DictOcrValues(&quot;VENDOR_NAME&quot;).ToString},{&quot;vendorStr&quot;,vendorStr}}]" ContinueOnError="True" DisplayName="RowsFromDeliveryNote_InvokeWorkflow_vendorResponseDictionary_74" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_4" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\vendorID.xaml&quot;]" />
                <If Condition="[vendorResponseCode = 200]" DisplayName="RowsFromDeliveryNote_If_vendorResponseCode_75" sap2010:WorkflowViewState.IdRef="If_12">
                  <If.Then>
                    <Sequence DisplayName="RowsFromDeliveryNote_Sequence_VendorIDSequence_76" sap2010:WorkflowViewState.IdRef="Sequence_21">
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorId_77" sap2010:WorkflowViewState.IdRef="Assign_31">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("vendorID"), String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <If Condition="[NOT vendorId = &quot;&quot;]" DisplayName="RowsFromDeliveryNote_If_vendorId_78" sap2010:WorkflowViewState.IdRef="If_11">
                        <If.Then>
                          <Assign DisplayName="RowsFromDeliveryNote_Assign_M3TotalTableRows_79" sap2010:WorkflowViewState.IdRef="Assign_100">
                            <Assign.To>
                              <OutArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="scg:List(s:String[])">[M3TotalTableRows.Where(Function(arr) arr.Length &gt; 1 AndAlso arr(7) = vendorId).ToList()]</InArgument>
                            </Assign.Value>
                          </Assign>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence DisplayName="RowsFromDeliveryNote_Sequence_SingleVendorSequence_80" sap2010:WorkflowViewState.IdRef="Sequence_23">
                <If Condition="[vendorList.Count = 1]" DisplayName="RowsFromDeliveryNote_If_vendorList_81" sap2010:WorkflowViewState.IdRef="If_13">
                  <If.Then>
                    <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorId_82" sap2010:WorkflowViewState.IdRef="Assign_33">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">[M3TotalTableRows(0)(7)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </If.Then>
                </If>
              </Sequence>
            </If.Else>
          </If>
          <If Condition="[vendorID = &quot;&quot;]" DisplayName="RowsFromDeliveryNote_If_vendorID_83" sap2010:WorkflowViewState.IdRef="If_17">
            <If.Then>
              <Sequence DisplayName="RowsFromDeliveryNote_Sequence_VendorAddressSequence_84" sap2010:WorkflowViewState.IdRef="Sequence_26">
                <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;tenantID&quot;,tenantID},{&quot;VendorName&quot;,DictOcrValues(&quot;VENDOR_NAME&quot;).ToString},{&quot;VendorAddress&quot;,DictOcrValues(&quot;VENDOR_ADDRESS&quot;).Tostring},{&quot;VendorPhone&quot;,DictOcrValues(&quot;VENDOR_PHONE&quot;).Tostring},{&quot;DictOcrValues&quot;,DictOcrValues}}]" ContinueOnError="True" DisplayName="RowsFromDeliveryNote_InvokeWorkflow_vendorResponseDictionary_85" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_5" OutputArguments="[vendorResponseDictionary]" ResponseCode="[vendorResponseCode]" WorkflowFile="[projectPath+&quot;\vendorIDAddressMatch.xaml&quot;]" />
                <If Condition="[vendorResponseCode = 200]" DisplayName="RowsFromDeliveryNote_If_vendorResponseCode_86" sap2010:WorkflowViewState.IdRef="If_15">
                  <If.Then>
                    <Sequence DisplayName="RowsFromDeliveryNote_Sequence_VendorAddressFoundSequence_87" sap2010:WorkflowViewState.IdRef="Sequence_24">
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_vendorId_88" sap2010:WorkflowViewState.IdRef="Assign_34">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[CType(vendorResponseDictionary("vendorID"), String)]</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Then>
                </If>
                <If Condition="[vendorId &lt;&gt; &quot;&quot;]" DisplayName="RowsFromDeliveryNote_If_vendorId_89" sap2010:WorkflowViewState.IdRef="If_16">
                  <If.Else>
                    <Sequence DisplayName="RowsFromDeliveryNote_Sequence_FinalMissingInfoSequence_90" sap2010:WorkflowViewState.IdRef="Sequence_25">
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_commentStatus_91" sap2010:WorkflowViewState.IdRef="Assign_35">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">Missing required Information</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="RowsFromDeliveryNote_Assign_Status_92" sap2010:WorkflowViewState.IdRef="Assign_36">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">MISSINGINFORMATION</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
          </If>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="RowsFromDeliveryNote_Append_Line_logFile_93" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[&quot;Vendor ID: &quot; + vendorId]" Source="[logFile]" />
        </Sequence>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="1146,62" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="1146,62" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="1146,62" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="1146,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="1146,62" />
      <sap2010:ViewStateData Id="Assign_79" sap:VirtualizedContainerService.HintSize="1024,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_12" sap:VirtualizedContainerService.HintSize="1024,22" />
      <sap2010:ViewStateData Id="Assign_80" sap:VirtualizedContainerService.HintSize="712,62" />
      <sap2010:ViewStateData Id="Assign_81" sap:VirtualizedContainerService.HintSize="712,62" />
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="712,62" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_63" sap:VirtualizedContainerService.HintSize="239.333333333333,258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="284.666666666667,410.666666666667" />
      <sap2010:ViewStateData Id="Sequence_64" sap:VirtualizedContainerService.HintSize="306.666666666667,534.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="464,688.666666666667" />
      <sap2010:ViewStateData Id="Sequence_65" sap:VirtualizedContainerService.HintSize="486,812.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_83" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Assign_84" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_85" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_86" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_66" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_87" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_13" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_88" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_89" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_90" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="284.666666666667,22" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="239.333333333333,258">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="284.666666666667,410.666666666667" />
      <sap2010:ViewStateData Id="Sequence_68" sap:VirtualizedContainerService.HintSize="306.666666666667,596.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_14" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_91" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Assign_92" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_93" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_70" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="Sequence_71" sap:VirtualizedContainerService.HintSize="486,1008">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="818.666666666667,1162" />
      <sap2010:ViewStateData Id="If_43" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="264,482.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_44" sap:VirtualizedContainerService.HintSize="464,636.666666666667" />
      <sap2010:ViewStateData Id="Sequence_73" sap:VirtualizedContainerService.HintSize="486,924.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_94" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_95" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_74" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_45" sap:VirtualizedContainerService.HintSize="776,1078.66666666667" />
      <sap2010:ViewStateData Id="Sequence_75" sap:VirtualizedContainerService.HintSize="798,1202.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_46" sap:VirtualizedContainerService.HintSize="1088,1356.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_96" sap:VirtualizedContainerService.HintSize="1088,62" />
      <sap2010:ViewStateData Id="Sequence_76" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_47" sap:VirtualizedContainerService.HintSize="712,966.666666666667" />
      <sap2010:ViewStateData Id="Sequence_77" sap:VirtualizedContainerService.HintSize="734,1396.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_97" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_98" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_99" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_78" sap:VirtualizedContainerService.HintSize="264,390">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_48" sap:VirtualizedContainerService.HintSize="1024,1550.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_79" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="1146,212.666666666667" />
      <sap2010:ViewStateData Id="Assign_27" sap:VirtualizedContainerService.HintSize="1146,62" />
      <sap2010:ViewStateData Id="Assign_28" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="Assign_29" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_19" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_4" sap:VirtualizedContainerService.HintSize="612,338.666666666667" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="612,62" />
      <sap2010:ViewStateData Id="InvokeWorkflow_4" sap:VirtualizedContainerService.HintSize="612,22" />
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_100" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_11" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_21" sap:VirtualizedContainerService.HintSize="486,442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="612,596" />
      <sap2010:ViewStateData Id="Sequence_22" sap:VirtualizedContainerService.HintSize="634,1364.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_33" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_23" sap:VirtualizedContainerService.HintSize="486,340">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="1146,1518.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_34" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_24" sap:VirtualizedContainerService.HintSize="264,186">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="464,340" />
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_25" sap:VirtualizedContainerService.HintSize="264,288">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="464,442" />
      <sap2010:ViewStateData Id="Sequence_26" sap:VirtualizedContainerService.HintSize="486,1008">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_17" sap:VirtualizedContainerService.HintSize="1146,1162">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="1146,22" />
      <sap2010:ViewStateData Id="Sequence_27" sap:VirtualizedContainerService.HintSize="1168,3771.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_18" sap:VirtualizedContainerService.HintSize="1294,3925.33333333333" />
      <sap2010:ViewStateData Id="Sequence_28" sap:VirtualizedContainerService.HintSize="1316,4049.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1356,4209.33333333333" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>