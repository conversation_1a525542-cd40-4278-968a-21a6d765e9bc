﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iae="clr-namespace:Infor.Activities.Email;assembly=Infor.Activities.Email"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:si="clr-namespace:System.IO;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="submails" Type="InArgument(scg:List(iae:Mail))" />
    <x:Property Name="emailsubjects" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceSource" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="seqnumber" Type="InArgument(x:String)" />
    <x:Property Name="DownloadTo" Type="InArgument(x:String)" />
    <x:Property Name="FileRenameFormat" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="poFilterValues" Type="InArgument(x:String)" />
    <x:Property Name="poFilterCondition" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="extractNumericFromPO" Type="InArgument(x:Boolean)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="processExpenseInvoice" Type="InArgument(x:Boolean)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="MasterDownloads" Type="InArgument(x:String)" />
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="mainStoreStats" Type="OutArgument(scg:List(scg:List(x:String)))" />
    <x:Property Name="strJsonString" Type="OutArgument(x:String)" />
    <x:Property Name="blnFailureExists" Type="OutArgument(x:Boolean)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>System.IO</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="TryCatch Preprocess Flow" sap2010:WorkflowViewState.IdRef="TryCatch_11">
    <TryCatch.Variables>
      <Variable x:TypeArguments="x:String" Name="SublogFile" />
      <Variable x:TypeArguments="scg:List(x:String)" Default="[new list( of string)]" Name="ListBulkNames" />
      <Variable x:TypeArguments="x:Int32" Name="intFailureMailCounter" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence DisplayName="Outlook Preprocess Sequence" sap2010:WorkflowViewState.IdRef="Sequence_105">
        <Sequence.Variables>
          <Variable x:TypeArguments="x:String" Name="inProgressFolder" />
          <Variable x:TypeArguments="x:Int32" Default="0" Name="pdfs_count" />
          <Variable x:TypeArguments="si:FileInfo" Name="fileDetails" />
          <Variable x:TypeArguments="x:Int32" Name="RenameCounter" />
          <Variable x:TypeArguments="scg:List(x:String)" Name="ListDownloadedFiles" />
          <Variable x:TypeArguments="x:Boolean" Name="attachmentsAvail" />
          <Variable x:TypeArguments="x:Int32" Name="processPoRespCode" />
          <Variable x:TypeArguments="x:String" Name="subDownloadFolder" />
          <Variable x:TypeArguments="x:String" Name="InvoiceFileName" />
          <Variable x:TypeArguments="scg:List(x:String)" Name="storeStats" />
          <Variable x:TypeArguments="x:String" Name="strException" />
        </Sequence.Variables>
        <ias:File_Create ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Create Sub Log File" sap2010:WorkflowViewState.IdRef="File_Create_4" Name="[&quot;SublogFile_&quot;+seqnumber+&quot;.txt&quot;]" OutputFile="[SublogFile]" Target="[configurationFolder+&quot;\OutlookDownloads\&quot;]" />
        <Assign DisplayName="Assign blnFailureExists" sap2010:WorkflowViewState.IdRef="Assign_240">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Boolean">[blnFailureExists]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Boolean">False</InArgument>
          </Assign.Value>
        </Assign>
        <Assign DisplayName="Initialize strJsonString" sap2010:WorkflowViewState.IdRef="Assign_205">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[strJsonString]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">
              <Literal x:TypeArguments="x:String" Value="" />
            </InArgument>
          </Assign.Value>
        </Assign>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SubLog Preprocess Start - Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_45" Line="[&quot;Logs from Preprocess Sequence - #&quot;+seqnumber.ToString+ &quot; - Start&quot;+Environment.NewLine+&quot;------------------------------------------------&quot;]" Source="[SublogFile]" />
        <Assign sap2010:WorkflowViewState.IdRef="Assign_155">
          <Assign.To>
            <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainStoreStats]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(of list(of string))]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_156">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Int32">1</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_157">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[inProgressFolder]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[configurationFolder+ "\InProgress"]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign sap2010:WorkflowViewState.IdRef="Assign_158">
          <Assign.To>
            <OutArgument x:TypeArguments="scg:List(x:String)">[emailsubjects]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
          </Assign.Value>
        </Assign>
        <Assign DisplayName="Assign intFailureMailCounter" sap2010:WorkflowViewState.IdRef="Assign_224">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Int32">[intFailureMailCounter]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Int32">1</InArgument>
          </Assign.Value>
        </Assign>
        <ForEach x:TypeArguments="iae:Mail" sap2010:Annotation.AnnotationText="All the attachments from the sub mails list SM1, SM2, SM3, SM4, SM5 will be downlaoded as part of this block of code. This block also includes additional functionality of handling duplicate file names and also changing .PDF to _copy.pdf" DisplayName="ForEach submails" sap2010:WorkflowViewState.IdRef="ForEach`1_39" Values="[submails]">
          <ActivityAction x:TypeArguments="iae:Mail">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="iae:Mail" Name="email" />
            </ActivityAction.Argument>
            <TryCatch DisplayName="TryCatch - Process Each email (Inside for each sequence)" sap2010:WorkflowViewState.IdRef="TryCatch_10">
              <TryCatch.Variables>
                <Variable x:TypeArguments="x:Boolean" Name="blnDownload" />
                <Variable x:TypeArguments="x:String" Name="MailCounter" />
                <Variable x:TypeArguments="x:String" Name="strFailureData">
                  <Variable.Default>
                    <Literal x:TypeArguments="x:String" Value="" />
                  </Variable.Default>
                </Variable>
              </TryCatch.Variables>
              <TryCatch.Try>
                <Sequence DisplayName="For each Mail Sequence" sap2010:WorkflowViewState.IdRef="Sequence_102">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="scg:List(x:String)" Name="MasterFiles" />
                    <Variable x:TypeArguments="scg:List(x:String)" Name="attachments" />
                    <Variable x:TypeArguments="x:Boolean" Name="pdfFileAvailable" />
                    <Variable x:TypeArguments="x:Double" Name="filelength" />
                    <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(Of String)]" Name="ListMasterNames" />
                  </Sequence.Variables>
                  <If Condition="[enableMessageBoxes]" DisplayName="Check If enableMessageBoxes" sap2010:WorkflowViewState.IdRef="If_63">
                    <If.Then>
                      <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="Message Box" sap2010:WorkflowViewState.IdRef="MessageBox_24" Selection="OK" Text="[&quot;Email Subject :&quot;+email.subject]" />
                    </If.Then>
                  </If>
                  <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[MasterDownloads]" DisplayName="Get Files in Directory - Master Downloads folder" FileType="All" Files="[MasterFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_17" IncludeSubDir="True" />
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_159">
                    <Assign.To>
                      <OutArgument x:TypeArguments="scg:List(x:String)">[ListMasterNames]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ForEach x:TypeArguments="x:String" DisplayName="Get FileNames from Master Downloads folder" sap2010:WorkflowViewState.IdRef="ForEach`1_36" Values="[MasterFiles]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                      </ActivityAction.Argument>
                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_55" MethodName="Add">
                        <InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[ListMasterNames]</InArgument>
                        </InvokeMethod.TargetObject>
                        <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).Tostring]</InArgument>
                      </InvokeMethod>
                    </ActivityAction>
                  </ForEach>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_202">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Boolean">[blnDownload]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Switch x:TypeArguments="x:String" DisplayName="Downlaod Attachments based on Email invoiceSource" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_24">
                    <x:Null x:Key="OutlookGraphEmail" />
                    <Sequence x:Key="OutlookClientEmail" DisplayName="OutlookClientEmail Sequence" sap2010:WorkflowViewState.IdRef="Sequence_91">
                      <iae:DownloadOutlookAttachment ResponseCode="{x:Null}" ContinueOnError="False" DisplayName="Download Outlook Attachment" Email="[email]" sap2010:WorkflowViewState.IdRef="DownloadOutlookAttachment_8" OutputFilePaths="[attachments]" Path="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" />
                      <Assign DisplayName="blnDownload Assign" sap2010:WorkflowViewState.IdRef="Assign_201">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[blnDownload]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                        </Assign.Value>
                      </Assign>
                    </Sequence>
                  </Switch>
                  <Switch x:TypeArguments="x:Boolean" DisplayName="Check blnDownload" Expression="[blnDownload]" sap2010:WorkflowViewState.IdRef="Switch`1_34">
                    <Sequence x:Key="True" DisplayName="blnDownload True" sap2010:WorkflowViewState.IdRef="Sequence_112">
                      <Assign DisplayName="Assign pdfFileAvailable as false" sap2010:WorkflowViewState.IdRef="Assign_160">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign sap2010:WorkflowViewState.IdRef="Assign_161">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[attachmentsAvail]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ForEach x:TypeArguments="x:String" DisplayName="Extract ZIP files in attachments" sap2010:WorkflowViewState.IdRef="ForEach`1_37" Values="[attachments]">
                        <ActivityAction x:TypeArguments="x:String">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="x:String" Name="attachment" />
                          </ActivityAction.Argument>
                          <If Condition="[attachment.contains(&quot;.zip&quot;) or attachment.contains(&quot;.ZIP&quot;)]" DisplayName="If .ZIP file Attachment" sap2010:WorkflowViewState.IdRef="If_64">
                            <If.Then>
                              <Sequence DisplayName="Extract Zip" sap2010:WorkflowViewState.IdRef="Sequence_92">
                                <Sequence.Variables>
                                  <Variable x:TypeArguments="x:String" Name="extractedFolderPath" />
                                  <Variable x:TypeArguments="scg:List(x:String)" Name="extractedFiles" />
                                  <Variable x:TypeArguments="x:String" Name="extractedFilePath" />
                                </Sequence.Variables>
                                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_46" Line="[&quot;ZIP File Extracted started for attachments in email - &quot;+email.subject]" Source="[SublogFile]" />
                                <ias:Directory_Extract ErrorCode="{x:Null}" Password="{x:Null}" ContinueOnError="False" DisplayName="Extract Directory" sap2010:WorkflowViewState.IdRef="Directory_Extract_5" OutputFile="[extractedFolderPath]" Source="[attachment]" Target="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" />
                                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete ZIP File" sap2010:WorkflowViewState.IdRef="File_Delete_17" Source="[attachment]" />
                              </Sequence>
                            </If.Then>
                          </If>
                        </ActivityAction>
                      </ForEach>
                      <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\&quot;+DownloadTo+seqnumber]" DisplayName="Get Files in OutlookDownloads" FileType="All" Files="[ListDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_18" IncludeSubDir="True" />
                      <ForEach x:TypeArguments="x:String" DisplayName="ForEach File in OutlookDownloads" sap2010:WorkflowViewState.IdRef="ForEach`1_38" Values="[ListDownloadedFiles]">
                        <ActivityAction x:TypeArguments="x:String">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="x:String" Name="item" />
                          </ActivityAction.Argument>
                          <Sequence DisplayName="Inside For each file in OutlookDownloads" sap2010:WorkflowViewState.IdRef="Sequence_100">
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_162">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Boolean">[attachmentsAvail]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Switch x:TypeArguments="x:Boolean" DisplayName="Rename .PDF files if any" Expression="[item.contains(&quot;.PDF&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_25">
                              <Sequence x:Key="True" DisplayName="PDF Sequence" sap2010:WorkflowViewState.IdRef="Sequence_93">
                                <Sequence.Variables>
                                  <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                                  <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                                </Sequence.Variables>
                                <Assign DisplayName="Assign pdfFileName" sap2010:WorkflowViewState.IdRef="Assign_163">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[Path.GetFileName(item).replace(".PDF","_copy.pdf")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Copy File" sap2010:WorkflowViewState.IdRef="File_Copy_7" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[item]" Target="[Path.GetDirectoryName(item)]" TargetFilename="[pdfFileName]" />
                                <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_18" Source="[item]" />
                                <Assign DisplayName="Assign attachment+_Copy" sap2010:WorkflowViewState.IdRef="Assign_164">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[item]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[item.replace(".PDF","_copy.pdf")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </Sequence>
                            </Switch>
                            <Switch x:TypeArguments="x:Boolean" DisplayName=".pdf Check If attachment.contains(&quot;.pdf&quot;)" Expression="[item.contains(&quot;.pdf&quot;)]" sap2010:WorkflowViewState.IdRef="Switch`1_27">
                              <Sequence x:Key="True" DisplayName="If .pdf extension" sap2010:WorkflowViewState.IdRef="Sequence_98">
                                <Sequence.Variables>
                                  <Variable x:TypeArguments="x:String" Name="MovedFilePath" />
                                </Sequence.Variables>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_165">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_166">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="si:FileInfo">[fileDetails]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="si:FileInfo">[New FileInfo(item)]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Assign DisplayName="fileLength Assign" sap2010:WorkflowViewState.IdRef="Assign_167">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Double">[filelength]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Double">[Math.Round(filedetails.Length / 1024.0 / 1024.0, 2)]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <If Condition="[filelength&lt;=5]" DisplayName="Check file Length" sap2010:WorkflowViewState.IdRef="If_65">
                                  <If.Then>
                                    <Sequence DisplayName="ProcessEmailAttachment" sap2010:WorkflowViewState.IdRef="Sequence_94">
                                      <Sequence.Variables>
                                        <Variable x:TypeArguments="x:String" Name="variable1" />
                                      </Sequence.Variables>
                                      <Assign DisplayName="Assign pdfFileAvailable to True" sap2010:WorkflowViewState.IdRef="Assign_168">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Boolean">[pdfFileAvailable]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_56" MethodName="Add">
                                        <InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:List(x:String)">[emailsubjects]</InArgument>
                                        </InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="x:String">[item.Substring(item.LastIndexOf("\"c)+1,(item.Length()-item.LastIndexOf("\"c))-1)]</InArgument>
                                      </InvokeMethod>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:Int32">[pdfs_count]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:Int32">[pdfs_count+1]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[Path.GetFilename(item)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to InProgress Folder" Expression="[ListMasterNames.Contains(Path.GetFileName(item))]" sap2010:WorkflowViewState.IdRef="Switch`1_26">
                                        <Sequence x:Key="True" DisplayName="File Name Already Present in Master Downloads" sap2010:WorkflowViewState.IdRef="Sequence_95">
                                          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_17">
                                            <iad:CommentOut.Activities>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_192">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[If(System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]"),System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_"),InvoiceFileName)]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </iad:CommentOut.Activities>
                                          </iad:CommentOut>
                                          <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_66">
                                            <If.Then>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_171">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </If.Then>
                                          </If>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_172">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[seqnumber+"_"+InvoiceFileName]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_190">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_12" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[MasterDownloads]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_&quot;+FileRenameFormat+&quot;_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                                        </Sequence>
                                        <Sequence x:Key="False" DisplayName="File Name not Already Present in Master Downloads Folder" sap2010:WorkflowViewState.IdRef="Sequence_96">
                                          <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_67">
                                            <If.Then>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@~`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </If.Then>
                                          </If>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_174">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[seqnumber+"_"+InvoiceFileName]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_191">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_13" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[MasterDownloads]" targetName="[InvoiceFileName]" />
                                        </Sequence>
                                      </Switch>
                                    </Sequence>
                                  </If.Then>
                                  <If.Else>
                                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_106">
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_200">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[Path.GetFilename(item)]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                      <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_71" MethodName="Add">
                                        <InvokeMethod.TargetObject>
                                          <InArgument x:TypeArguments="scg:List(x:String)">[ListBulkNames]</InArgument>
                                        </InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="x:String">[InvoiceFileName]</InArgument>
                                      </InvokeMethod>
                                      <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to BulkFiles Folder" Expression="[ListBulkNames.Contains(InvoiceFileName)]" sap2010:WorkflowViewState.IdRef="Switch`1_30">
                                        <Sequence x:Key="True" DisplayName="File Name Already Present in Bulk Files" sap2010:WorkflowViewState.IdRef="Sequence_107">
                                          <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_72">
                                            <If.Then>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_194">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </If.Then>
                                          </If>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_195">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[seqnumber+"_"+InvoiceFileName]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_196">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_15" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\OutlookDownloads\BulkFiles&quot;]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_&quot;+FileRenameFormat+&quot;_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                                        </Sequence>
                                        <Sequence x:Key="False" DisplayName="File Name not Already Present in Master Downloads Folder" sap2010:WorkflowViewState.IdRef="Sequence_108">
                                          <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_73">
                                            <If.Then>
                                              <Assign sap2010:WorkflowViewState.IdRef="Assign_197">
                                                <Assign.To>
                                                  <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                                </Assign.To>
                                                <Assign.Value>
                                                  <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@~`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                                </Assign.Value>
                                              </Assign>
                                            </If.Then>
                                          </If>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_198">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[seqnumber+"_"+InvoiceFileName]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <Assign sap2010:WorkflowViewState.IdRef="Assign_199">
                                            <Assign.To>
                                              <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                            </Assign.To>
                                            <Assign.Value>
                                              <InArgument x:TypeArguments="x:String">[InvoiceFileName.Replace("~","")]</InArgument>
                                            </Assign.Value>
                                          </Assign>
                                          <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_16" OutputFile="[MovedFilePath]" OverwriteFile="False" Source="[item]" Target="[configurationFolder+&quot;\OutlookDownloads\BulkFiles&quot;]" targetName="[InvoiceFileName]" />
                                        </Sequence>
                                      </Switch>
                                      <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_19">
                                        <iad:CommentOut.Activities>
                                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="File length &gt;5 MB Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_47" Line="[&quot;File length is greater than 5 MB. File name is - &quot;+Path.getfilename(item)]" Source="[SublogFile]" />
                                        </iad:CommentOut.Activities>
                                      </iad:CommentOut>
                                    </Sequence>
                                  </If.Else>
                                </If>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_175">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                                  </Assign.Value>
                                </Assign>
                                <Sequence DisplayName="Storing Stats Sequence" sap2010:WorkflowViewState.IdRef="Sequence_97">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_176">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="scg:List(x:String)">[storeStats]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[New List(of String)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <InvokeMethod DisplayName="email Account InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_57" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[emailAccount]</InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="email Sender InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_58" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[email.sender]</InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="email Receiver InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_59" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[emailAccount]</InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="email ReceivedTime InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_60" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[email.receivedTime]</InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="email Subject InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_61" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[email.subject]</InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="FileName InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_62" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[item.Substring(item.LastIndexOf("\"c)+1,(item.Length()-item.LastIndexOf("\"c))-1)]</InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="Execution start time InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_63" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">[System.DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")]</InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="Execution EndTime InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_64" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">
                                      <Literal x:TypeArguments="x:String" Value="" />
                                    </InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="Status InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_65" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">
                                      <Literal x:TypeArguments="x:String" Value="" />
                                    </InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="Comments InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_66" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">
                                      <Literal x:TypeArguments="x:String" Value="" />
                                    </InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="ProcessID InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_67" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">
                                      <Literal x:TypeArguments="x:String" Value="" />
                                    </InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="processfilename InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_68" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="x:String">
                                      <Literal x:TypeArguments="x:String" Value="" />
                                    </InArgument>
                                  </InvokeMethod>
                                  <InvokeMethod DisplayName="email Account InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_69" MethodName="Add">
                                    <InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainStoreStats]</InArgument>
                                    </InvokeMethod.TargetObject>
                                    <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                  </InvokeMethod>
                                </Sequence>
                              </Sequence>
                              <ias:File_Delete ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Delete File" sap2010:WorkflowViewState.IdRef="File_Delete_19" Source="[item]" />
                            </Switch>
                            <If Condition="[item.EndsWith(&quot;.pdf&quot;) OR item.EndsWith(&quot;.PDF&quot;) OR item.EndsWith(&quot;.txt&quot;) OR item.EndsWith(&quot;.jpg&quot;) OR item.EndsWith(&quot;.jpeg&quot;) OR item.EndsWith(&quot;.png&quot;) OR item.EndsWith(&quot;.zip&quot;) OR item.EndsWith(&quot;.ZIP&quot;)]" DisplayName="Check other file extensions" sap2010:WorkflowViewState.IdRef="If_69">
                              <If.Else>
                                <Sequence DisplayName="Other file extensions Sequence" sap2010:WorkflowViewState.IdRef="Sequence_99">
                                  <Sequence.Variables>
                                    <Variable x:TypeArguments="x:String" Name="status" />
                                    <Variable x:TypeArguments="x:String" Name="statusComments" />
                                    <Variable x:TypeArguments="x:String" Name="fileName" />
                                    <Variable x:TypeArguments="x:String" Name="message" />
                                    <Variable x:TypeArguments="x:String" Name="emailSubject" />
                                    <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                                    <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
                                    <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                                    <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                                    <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
                                  </Sequence.Variables>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_177">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_178">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">Wrong Format of the document</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_179">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[Path.GetFileName(item)]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_180">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_181">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[email.Subject]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_182">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:String">[email.ReceivedTime]</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_68">
                                    <If.Then>
                                      <Assign sap2010:WorkflowViewState.IdRef="Assign_183">
                                        <Assign.To>
                                          <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                        </Assign.To>
                                        <Assign.Value>
                                          <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
                                        </Assign.Value>
                                      </Assign>
                                    </If.Then>
                                  </If>
                                  <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_8" Template="{}{&#xA;    'message': '{{%msg%}}',&#xA;    'parameters': [&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_STATUS',&#xA;            'value': '{{%status%}}',&#xA;            'label': 'Status',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Subject',&#xA;            'value': '{{%Subject%}}',&#xA;            'label': 'Subject',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_RcvdTime',&#xA;            'value': '{{%RcvdTime%}}',&#xA;            'label': 'Recieved Time',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_FileName',&#xA;            'value': '{{%fileName%}}',&#xA;            'label': 'File Name',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Remarks',&#xA;            'value': '{{%Remarks%}}',&#xA;            'label': 'Remarks',&#xA;            'readOnly': true&#xA;        }&#xA;    ],&#xA;    'category': 'RPA',&#xA;    'distribution': [&#xA;        {&#xA;            'identifier': '{{%userIdentifier%}}',&#xA;            'type': '{{%distributionType%}}',&#xA;            'sendMail': false&#xA;        }&#xA;    ]&#xA;}&#xA;" Text="[notificationRequestStr]">
                                    <ias:Template_Apply.Values>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>status</x:String>
                                          <x:String>userIdentifier</x:String>
                                          <x:String>distributionType</x:String>
                                          <x:String>Remarks</x:String>
                                          <x:String>RcvdTime</x:String>
                                          <x:String>Subject</x:String>
                                          <x:String>msg</x:String>
                                          <x:String>fileName</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="8">
                                          <x:String>status</x:String>
                                          <x:String>userIdentifier</x:String>
                                          <x:String>distributionType</x:String>
                                          <x:String>statusComments</x:String>
                                          <x:String>emailReceivedTime</x:String>
                                          <x:String>emailSubject</x:String>
                                          <x:String>message</x:String>
                                          <x:String>fileName</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </ias:Template_Apply.Values>
                                  </ias:Template_Apply>
                                  <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_8" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
                                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="False" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                                    <iai:IONAPIRequestWizard.QueryParameters>
                                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>logicalId</x:String>
                                        </scg:List>
                                        <scg:List x:TypeArguments="x:String" Capacity="4">
                                          <x:String>lid://infor.rpa.1</x:String>
                                        </scg:List>
                                      </scg:List>
                                    </iai:IONAPIRequestWizard.QueryParameters>
                                  </iai:IONAPIRequestWizard>
                                </Sequence>
                              </If.Else>
                            </If>
                            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_16">
                              <iad:CommentOut.Activities>
                                <If Condition="[pdfFileAvailable]" sap2010:WorkflowViewState.IdRef="If_70">
                                  <If.Then>
                                    <InvokeMethod DisplayName="email Account InvokeMethod" sap2010:WorkflowViewState.IdRef="InvokeMethod_70" MethodName="Add">
                                      <InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainStoreStats]</InArgument>
                                      </InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[storeStats]</InArgument>
                                    </InvokeMethod>
                                  </If.Then>
                                </If>
                              </iad:CommentOut.Activities>
                            </iad:CommentOut>
                          </Sequence>
                        </ActivityAction>
                      </ForEach>
                      <Switch x:TypeArguments="x:Boolean" DisplayName="If attachments Avail False" Expression="[attachmentsAvail]" sap2010:WorkflowViewState.IdRef="Switch`1_28">
                        <Sequence x:Key="False" DisplayName="No Attachment Available" sap2010:WorkflowViewState.IdRef="Sequence_101">
                          <Sequence.Variables>
                            <Variable x:TypeArguments="x:String" Name="status" />
                            <Variable x:TypeArguments="x:String" Name="statusComments" />
                            <Variable x:TypeArguments="x:String" Name="fileName" />
                            <Variable x:TypeArguments="x:String" Name="message" />
                            <Variable x:TypeArguments="x:String" Name="emailSubject" />
                            <Variable x:TypeArguments="x:String" Name="emailReceivedTime" />
                            <Variable x:TypeArguments="x:String" Name="notificationRequestStr" />
                            <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                            <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                            <Variable x:TypeArguments="x:Int32" Name="notificationResponseCode" />
                          </Sequence.Variables>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_184">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[status]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">FAILURE</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_185">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[statusComments]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">["No attachments available."]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_186">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[message]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">["RPA M3 IP" +" - " + status + "( "+statusComments+")"]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_187">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[email.Subject]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <Assign sap2010:WorkflowViewState.IdRef="Assign_188">
                            <Assign.To>
                              <OutArgument x:TypeArguments="x:String">[emailReceivedTime]</OutArgument>
                            </Assign.To>
                            <Assign.Value>
                              <InArgument x:TypeArguments="x:String">[email.ReceivedTime]</InArgument>
                            </Assign.Value>
                          </Assign>
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_48" Line="No pdf files available to process" Source="[logFIle]" />
                          <If Condition="[emailSubject.contains(&quot;'&quot;)]" sap2010:WorkflowViewState.IdRef="If_71">
                            <If.Then>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_189">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[emailSubject]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String">[emailSubject.Replace("'","")]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </If.Then>
                          </If>
                          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_9" Template="{}{&#xA;    'message': '{{%msg%}}',&#xA;    'parameters': [&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_STATUS',&#xA;            'value': '{{%status%}}',&#xA;            'label': 'Status',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Subject',&#xA;            'value': '{{%Subject%}}',&#xA;            'label': 'Subject',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_RcvdTime',&#xA;            'value': '{{%RcvdTime%}}',&#xA;            'label': 'Recieved Time',&#xA;            'readOnly': true&#xA;        },&#xA;        {&#xA;            'dataType': 'STRING',&#xA;            'name': 'RPA_INVOICEPROCESSING_Remarks',&#xA;            'value': '{{%Remarks%}}',&#xA;            'label': 'Remarks',&#xA;            'readOnly': true&#xA;        }&#xA;    ],&#xA;    'category': 'RPA',&#xA;    'distribution': [&#xA;        {&#xA;            'identifier': '{{%userIdentifier%}}',&#xA;            'type': '{{%distributionType%}}',&#xA;            'sendMail': false&#xA;        }&#xA;    ]&#xA;}&#xA;" Text="[notificationRequestStr]">
                            <ias:Template_Apply.Values>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                  <x:String>status</x:String>
                                  <x:String>userIdentifier</x:String>
                                  <x:String>distributionType</x:String>
                                  <x:String>Remarks</x:String>
                                  <x:String>RcvdTime</x:String>
                                  <x:String>Subject</x:String>
                                  <x:String>msg</x:String>
                                  <x:String>fileName</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="8">
                                  <x:String>status</x:String>
                                  <x:String>userIdentifier</x:String>
                                  <x:String>distributionType</x:String>
                                  <x:String>statusComments</x:String>
                                  <x:String>emailReceivedTime</x:String>
                                  <x:String>emailSubject</x:String>
                                  <x:String>message</x:String>
                                  <x:String>fileName</x:String>
                                </scg:List>
                              </scg:List>
                            </ias:Template_Apply.Values>
                          </ias:Template_Apply>
                          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_9" JTokenObject="[notificationToken]" JTokenString="[notificationRequestStr]" />
                          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" Headers="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" PostData="[notificationToken.tostring]" Response="[notificationResponse]" ResponseCode="[notificationResponseCode]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                            <iai:IONAPIRequestWizard.QueryParameters>
                              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>logicalId</x:String>
                                </scg:List>
                                <scg:List x:TypeArguments="x:String" Capacity="4">
                                  <x:String>lid://infor.rpa.1</x:String>
                                </scg:List>
                              </scg:List>
                            </iai:IONAPIRequestWizard.QueryParameters>
                          </iai:IONAPIRequestWizard>
                        </Sequence>
                      </Switch>
                      <Switch x:TypeArguments="x:Boolean" DisplayName="Is any PDF file available" Expression="[pdfFileAvailable]" sap2010:WorkflowViewState.IdRef="Switch`1_29">
                        <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_49" Line="[&quot;pdfFileAvailable value is &quot;+pdfFileAvailable.tostring+&quot; for email with subject - &quot;+email.subject]" Source="[SublogFile]" />
                      </Switch>
                    </Sequence>
                  </Switch>
                </Sequence>
              </TryCatch.Try>
              <TryCatch.Catches>
                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_10">
                  <ActivityAction x:TypeArguments="s:Exception">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                    </ActivityAction.Argument>
                    <Sequence DisplayName="Catch Block For Each Mail - Sequence" sap2010:WorkflowViewState.IdRef="Sequence_103">
                      <Assign DisplayName="Assign strException" sap2010:WorkflowViewState.IdRef="Assign_210">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[strException]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[exception.Message]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <ias:StudioWriteLine ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Write Line" sap2010:WorkflowViewState.IdRef="StudioWriteLine_2" Line="[&quot;Error occurred while downloading attachments from email with subject - &quot;+email.Subject]" />
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="For each mail catch block - Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_50" Line="[&quot;Exception occurred in OutlookPreprocess XAML for Email below &quot;+Environment.newline+&quot;Email Subject is -&quot;+email.subject+Environment.newline+&quot;Email Received time is -&quot;+email.ReceivedTime.ToString+Environment.newline+&quot;Exception type is - &quot;+exception.GetType().Name+&quot;.&quot;]" Source="[SublogFile]" />
                    </Sequence>
                  </ActivityAction>
                </Catch>
              </TryCatch.Catches>
              <TryCatch.Finally>
                <If Condition="[Not blnDownload]" DisplayName="Mark emails as Unread if Download Fails" sap2010:WorkflowViewState.IdRef="If_76">
                  <If.Then>
                    <Sequence DisplayName="Failure Data Collection" sap2010:WorkflowViewState.IdRef="Sequence_116">
                      <Assign DisplayName="Assign blnFailureExists" sap2010:WorkflowViewState.IdRef="Assign_241">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Boolean">[blnFailureExists]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="Assign MailCounter" sap2010:WorkflowViewState.IdRef="Assign_234">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[MailCounter]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">[Right(FileRenameFormat,1)+intFailureMailCounter.tostring]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="Assign strFailureData" sap2010:WorkflowViewState.IdRef="Assign_236">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[strFailureData]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[("🗂️Email Subject: " &amp; email.Subject).PadRight(60, " "c) &amp;
("⚠️ Exception Message: " &amp; strException).PadRight(60, " "c) &amp;
("🕒 Email Received Time: " &amp; email.ReceivedTime.ToString).PadRight(60, " "c)]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="Assign strJsonString" sap2010:WorkflowViewState.IdRef="Assign_237">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[strJsonString]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(strJsonString="", 
"{" &amp; 
" 'dataType': 'STRING'," &amp;
" 'name': '" &amp; MailCounter &amp; "'," &amp;
" 'value': '" &amp; strFailureData &amp; "'," &amp;
" 'label': 'Failure Details - " &amp; MailCounter &amp; "'," &amp; 
" 'readOnly': true" &amp; 
" }",
strJsonString &amp; "," &amp; 
"{" &amp; 
" 'dataType': 'STRING'," &amp;
" 'name': '" &amp; MailCounter &amp; "'," &amp;
" 'value': '" &amp; strFailureData &amp; "'," &amp;
" 'label': 'Failure Details - " &amp; MailCounter &amp; "'," &amp; 
" 'readOnly': true" &amp; 
" }")]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="Assign MailCounter NULL" sap2010:WorkflowViewState.IdRef="Assign_238">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:String">[MailCounter]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:String">
                            <Literal x:TypeArguments="x:String" Value="" />
                          </InArgument>
                        </Assign.Value>
                      </Assign>
                      <Assign DisplayName="Assign intFailureMailCounter" sap2010:WorkflowViewState.IdRef="Assign_239">
                        <Assign.To>
                          <OutArgument x:TypeArguments="x:Int32">[intFailureMailCounter]</OutArgument>
                        </Assign.To>
                        <Assign.Value>
                          <InArgument x:TypeArguments="x:Int32">[intFailureMailCounter+1]</InArgument>
                        </Assign.Value>
                      </Assign>
                      <Switch x:TypeArguments="x:String" DisplayName="Mark Failure emails as Unread invoiceSource" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_36">
                        <iae:MarkOutlookAsRead ErrorCode="{x:Null}" x:Key="OutlookClientEmail" ContinueOnError="True" DisplayName="Mark Outlook Emails as Read/Unread" sap2010:WorkflowViewState.IdRef="MarkOutlookAsRead_4" Mail="[email]" MarkAsRead="False" />
                        <x:Null x:Key="OutlookGraphEmail" />
                      </Switch>
                    </Sequence>
                  </If.Then>
                </If>
              </TryCatch.Finally>
            </TryCatch>
          </ActivityAction>
        </ForEach>
        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SubLog Preprocess End - Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_51" Line="[&quot;Logs from Preprocess Sequence - #&quot;+seqnumber.ToString+ &quot;End&quot;+Environment.NewLine+Environment.NewLine+Environment.NewLine]" Source="[SublogFile]" />
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_11">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="SubLog Preprocess Start - Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_52" Line="[&quot;Exception Occurred in Preprocess flow Sequence - #&quot;+seqnumber.ToString+&quot; with exception type &quot;+exception.GetType().Name+&quot;.&quot;]" Source="[SublogFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="File_Create_4" sap:VirtualizedContainerService.HintSize="548,22" />
      <sap2010:ViewStateData Id="Assign_240" sap:VirtualizedContainerService.HintSize="548,62" />
      <sap2010:ViewStateData Id="Assign_205" sap:VirtualizedContainerService.HintSize="548,62" />
      <sap2010:ViewStateData Id="Append_Line_45" sap:VirtualizedContainerService.HintSize="548,22" />
      <sap2010:ViewStateData Id="Assign_155" sap:VirtualizedContainerService.HintSize="548,62" />
      <sap2010:ViewStateData Id="Assign_156" sap:VirtualizedContainerService.HintSize="548,62" />
      <sap2010:ViewStateData Id="Assign_157" sap:VirtualizedContainerService.HintSize="548,62" />
      <sap2010:ViewStateData Id="Assign_158" sap:VirtualizedContainerService.HintSize="548,62" />
      <sap2010:ViewStateData Id="Assign_224" sap:VirtualizedContainerService.HintSize="548,62" />
      <sap2010:ViewStateData Id="MessageBox_24" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_63" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_17" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_159" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_55" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_36" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_202" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="DownloadOutlookAttachment_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_201" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_91" sap:VirtualizedContainerService.HintSize="264,245.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_24" sap:VirtualizedContainerService.HintSize="476.666666666667,274.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_160" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_161" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_46" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Directory_Extract_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="File_Delete_17" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_92" sap:VirtualizedContainerService.HintSize="222,270">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_64" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_37" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_18" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_162" sap:VirtualizedContainerService.HintSize="596,60" />
      <sap2010:ViewStateData Id="Assign_163" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Copy_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="File_Delete_18" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_164" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_93" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_25" sap:VirtualizedContainerService.HintSize="596,138">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_165" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_166" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="554,62" />
      <sap2010:ViewStateData Id="Assign_168" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_56" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_192" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="CommentOut_17" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_171" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_66" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_172" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_190" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_12" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_95" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_67" sap:VirtualizedContainerService.HintSize="464,216">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_174" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_191" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="File_Move_13" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Sequence_96" sap:VirtualizedContainerService.HintSize="486,601.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_26" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_94" sap:VirtualizedContainerService.HintSize="264,656.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_200" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="InvokeMethod_71" sap:VirtualizedContainerService.HintSize="476.666666666667,134" />
      <sap2010:ViewStateData Id="Assign_194" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_72" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_195" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_196" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_15" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_107" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_197" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_73" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_198" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_199" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_16" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_30" sap:VirtualizedContainerService.HintSize="476.666666666667,274.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_47" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_19" sap:VirtualizedContainerService.HintSize="476.666666666667,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_106" sap:VirtualizedContainerService.HintSize="498.666666666667,772.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_65" sap:VirtualizedContainerService.HintSize="788.666666666667,926.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_175" sap:VirtualizedContainerService.HintSize="788.666666666667,62" />
      <sap2010:ViewStateData Id="Assign_176" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_57" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_58" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_59" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_60" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_61" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_62" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_63" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_64" sap:VirtualizedContainerService.HintSize="242,134" />
      <sap2010:ViewStateData Id="InvokeMethod_65" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_66" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_67" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_68" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_69" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Sequence_97" sap:VirtualizedContainerService.HintSize="554,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_98" sap:VirtualizedContainerService.HintSize="810.666666666667,1551.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Delete_19" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_27" sap:VirtualizedContainerService.HintSize="596,164.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_177" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_178" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_179" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_180" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_181" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_182" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_183" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_68" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_99" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_69" sap:VirtualizedContainerService.HintSize="596,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_70" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="If_70" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_16" sap:VirtualizedContainerService.HintSize="596,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_100" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_38" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_184" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_185" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_186" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_187" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_188" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_48" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_189" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_71" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_101" sap:VirtualizedContainerService.HintSize="264,934.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_28" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_49" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_29" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_112" sap:VirtualizedContainerService.HintSize="264,710">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_34" sap:VirtualizedContainerService.HintSize="476.666666666667,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_102" sap:VirtualizedContainerService.HintSize="498.666666666667,942.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_210" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="StudioWriteLine_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Append_Line_50" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_103" sap:VirtualizedContainerService.HintSize="264,305">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_10" sap:VirtualizedContainerService.HintSize="503.333333333333,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_241" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_234" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_236" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_237" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_238" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_239" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="MarkOutlookAsRead_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_36" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_116" sap:VirtualizedContainerService.HintSize="264,795.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_76" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_10" sap:VirtualizedContainerService.HintSize="517.333333333333,1180.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_39" sap:VirtualizedContainerService.HintSize="548,1391.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
            <x:Boolean x:Key="IsAnnotationDocked">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_51" sap:VirtualizedContainerService.HintSize="546,22" />
      <sap2010:ViewStateData Id="Sequence_105" sap:VirtualizedContainerService.HintSize="570,2415.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_52" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_11" sap:VirtualizedContainerService.HintSize="574.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_11" sap:VirtualizedContainerService.HintSize="588.666666666667,2693.33333333333" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="628.666666666667,2813.33333333333" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>