﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="approvalLists" Type="InArgument(scg:List(scg:List(x:String)))" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_9">
    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_1" Values="[approvalLists]">
      <ActivityAction x:TypeArguments="scg:List(x:String)">
        <ActivityAction.Argument>
          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="part" />
        </ActivityAction.Argument>
        <Sequence sap2010:WorkflowViewState.IdRef="Sequence_8">
          <Sequence.Variables>
            <Variable x:TypeArguments="iru:ResponseObject" Name="paymentRespCode" />
            <Variable x:TypeArguments="x:Int32" Name="paymentStatus" />
            <Variable x:TypeArguments="x:Boolean" Name="KeepLooping" />
            <Variable x:TypeArguments="x:Int32" Name="loopBreak" />
            <Variable x:TypeArguments="x:Int32" Name="ValidateStatus" />
            <Variable x:TypeArguments="x:String" Name="supa" />
            <Variable x:TypeArguments="x:String" Name="inbnValue" />
            <Variable x:TypeArguments="x:String" Name="sino" />
            <Variable x:TypeArguments="x:String" Name="vendorId" />
            <Variable x:TypeArguments="x:String" Name="inyr" />
            <Variable x:TypeArguments="x:String" Name="division" />
          </Sequence.Variables>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_1">
            <Assign.To>
              <OutArgument x:TypeArguments="x:Int32">[loopBreak]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:Int32">1</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_2">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[inbnValue]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[part(0)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[sino]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[part(1)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_4">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[part(2)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_5">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[inyr]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[part(3)]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign sap2010:WorkflowViewState.IdRef="Assign_6">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[part(4)]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="[&quot;Approval process for the invoice number: &quot; + sino]" Source="[logfile]" />
          <DoWhile sap2010:WorkflowViewState.IdRef="DoWhile_1">
            <DoWhile.Variables>
              <Variable x:TypeArguments="iru:ResponseObject" Name="GetHeadRespObj" />
              <Variable x:TypeArguments="x:String" Name="bist" />
            </DoWhile.Variables>
            <DoWhile.Condition>[bist &lt;&gt; "0" AND supa = "50" AND loopBreak &lt;51]</DoWhile.Condition>
            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_2">
              <Sequence.Variables>
                <Variable x:TypeArguments="x:Int32" Name="loopBreak1" />
                <Variable x:TypeArguments="njl:JToken" Name="variable1" />
              </Sequence.Variables>
              <Delay Duration="00:00:02" sap2010:WorkflowViewState.IdRef="Delay_1" />
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Check the satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[GetHeadRespObj]" StatusCode="[ValidateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/GetHead&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>INBN</x:String>
                      <x:String>DIVI</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>inbnValue</x:String>
                      <x:String>division</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_7">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[supa]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("SUPA").ToString]</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_8">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Int32">[loopBreak1]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Int32">1</InArgument>
                </Assign.Value>
              </Assign>
              <While sap2010:WorkflowViewState.IdRef="While_1" Condition="[supa = &quot;10&quot; And loopbreak1 &lt; 5]">
                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_1">
                  <Delay Duration="00:00:04" sap2010:WorkflowViewState.IdRef="Delay_2" />
                  <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="Check the satus IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_2" Response="[GetHeadRespObj]" StatusCode="[ValidateStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS450MI/GetHead&quot;]">
                    <iai:IONAPIRequestWizard.Headers>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>Accept</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>application/json</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.Headers>
                    <iai:IONAPIRequestWizard.QueryParameters>
                      <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>INBN</x:String>
                          <x:String>DIVI</x:String>
                        </scg:List>
                        <scg:List x:TypeArguments="x:String" Capacity="4">
                          <x:String>inbnValue</x:String>
                          <x:String>division</x:String>
                        </scg:List>
                      </scg:List>
                    </iai:IONAPIRequestWizard.QueryParameters>
                  </iai:IONAPIRequestWizard>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_9">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[supa]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("SUPA").ToString]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <Assign sap2010:WorkflowViewState.IdRef="Assign_10">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:Int32">[loopBreak1]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:Int32">[loopBreak1 + 1]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </While>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_11">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[bist]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[GetHeadRespObj.ReadAsJson("results")(0)("records")(0)("BIST").ToString]</InArgument>
                </Assign.Value>
              </Assign>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[&quot;Validation status iterations: &quot; + loopbreak.ToString + &quot;  bist: &quot; + bist + &quot; supa: &quot; + supa]" Source="[logfile]" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_12">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Int32">[loopBreak]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Int32">[loopBreak + 1]</InArgument>
                </Assign.Value>
              </Assign>
            </Sequence>
          </DoWhile>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" Response="[paymentRespCode]" StatusCode="[paymentStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS110MI/ApproveInvoice&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>Accept</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>application/json</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>SUNO</x:String>
                  <x:String>DIVI</x:String>
                  <x:String>SINO</x:String>
                  <x:String>INYR</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>vendorId</x:String>
                  <x:String>division</x:String>
                  <x:String>sino</x:String>
                  <x:String>inyr</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <If Condition="[paymentStatus = 200]" sap2010:WorkflowViewState.IdRef="If_3">
            <If.Then>
              <Sequence sap2010:WorkflowViewState.IdRef="Sequence_7">
                <Assign sap2010:WorkflowViewState.IdRef="Assign_13">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Boolean">[KeepLooping]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Boolean">True</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_14">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:Int32">[loopBreak]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:Int32">0</InArgument>
                  </Assign.Value>
                </Assign>
                <If Condition="[supa = &quot;90&quot;]" sap2010:WorkflowViewState.IdRef="If_2">
                  <If.Then>
                    <Sequence sap2010:WorkflowViewState.IdRef="Sequence_6">
                      <DoWhile sap2010:WorkflowViewState.IdRef="DoWhile_2" Condition="[KeepLooping and LoopBreak &lt; 10]">
                        <TryCatch sap2010:WorkflowViewState.IdRef="TryCatch_1">
                          <TryCatch.Variables>
                            <Variable x:TypeArguments="x:String" Name="errorMsg" />
                          </TryCatch.Variables>
                          <TryCatch.Try>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_3">
                              <Delay Duration="00:00:01" sap2010:WorkflowViewState.IdRef="Delay_3" />
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_15">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:String">[errorMsg]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:String" xml:space="preserve">[paymentRespCode.ReadAsJson("results")(0)("errorMessage").ToString + "  in APS110."]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[errorMsg]" Source="[logfile]" />
                              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_4" Response="[paymentRespCode]" StatusCode="[paymentStatus]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/APS110MI/ApproveInvoice&quot;]">
                                <iai:IONAPIRequestWizard.Headers>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>Accept</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>application/json</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.Headers>
                                <iai:IONAPIRequestWizard.QueryParameters>
                                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>SUNO</x:String>
                                      <x:String>DIVI</x:String>
                                      <x:String>SINO</x:String>
                                      <x:String>INYR</x:String>
                                    </scg:List>
                                    <scg:List x:TypeArguments="x:String" Capacity="4">
                                      <x:String>vendorId</x:String>
                                      <x:String>division</x:String>
                                      <x:String>sino</x:String>
                                      <x:String>inyr</x:String>
                                    </scg:List>
                                  </scg:List>
                                </iai:IONAPIRequestWizard.QueryParameters>
                              </iai:IONAPIRequestWizard>
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_16">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="x:Int32">[loopBreak]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="x:Int32">[loopBreak + 1]</InArgument>
                                </Assign.Value>
                              </Assign>
                            </Sequence>
                          </TryCatch.Try>
                          <TryCatch.Catches>
                            <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                              <ActivityAction x:TypeArguments="s:Exception">
                                <ActivityAction.Argument>
                                  <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                                </ActivityAction.Argument>
                                <Sequence sap2010:WorkflowViewState.IdRef="Sequence_4">
                                  <Assign sap2010:WorkflowViewState.IdRef="Assign_17">
                                    <Assign.To>
                                      <OutArgument x:TypeArguments="x:Boolean">[KeepLooping]</OutArgument>
                                    </Assign.To>
                                    <Assign.Value>
                                      <InArgument x:TypeArguments="x:Boolean">False</InArgument>
                                    </Assign.Value>
                                  </Assign>
                                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="Approved for payment" Source="[logfile]" />
                                </Sequence>
                              </ActivityAction>
                            </Catch>
                          </TryCatch.Catches>
                        </TryCatch>
                      </DoWhile>
                      <If Condition="[LoopBreak &gt;= 10 AND KeepLooping]" sap2010:WorkflowViewState.IdRef="If_1">
                        <If.Then>
                          <Sequence sap2010:WorkflowViewState.IdRef="Sequence_5">
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="Invoice created and validated in the M3 but not approved for payment due to load on the system. Please approve them manually." Source="[logfile]" />
                          </Sequence>
                        </If.Then>
                      </If>
                    </Sequence>
                  </If.Then>
                  <If.Else>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="[&quot;Invoice status is: &quot;+ supa]" Source="[logfile]" />
                  </If.Else>
                </If>
              </Sequence>
            </If.Then>
            <If.Else>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="Error while approving the payment" Source="[logfile]" />
            </If.Else>
          </If>
        </Sequence>
      </ActivityAction>
    </ForEach>
    <sads:DebugSymbol.Symbol>d1RDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXGFwcHJvdmFsLnhhbWx1RAOTAw4CAQFFBZEDDwIBAkWYAUWpAQMBswFKCY8DFAIBA1gLXxQDAa8BYAtnFAMBqgFoC28UAwGlAXALdxQDAaABeAt/FAMBmwGAAQuHARQDAZYBiAELiAHtAQMBkQGJAQv8ARUCAVP9AQuYAiUCAUyZAguOAxACAQRdNV02AwGyAVo2WkEDAbABZTZlPwMBrQFiN2JCAwGrAW02bT8DAagBajdqPQMBpgF1NnU/AwGjAXI3ckEDAaEBfTZ9PwMBngF6N3o9AwGcAYUBNoUBPwMBmQGCATeCAUEDAZcBiAGXAYgB1wEDAZQBiAHfAYgB6gEDAZIBjgEgjgFYAwGNAY8BDfsBGAIBVP0BkAL9AaMCAgFR/QHFAv0BkAMCAU/9Aa8C/QHAAgIBTZkCGZkCMAIBBZsCD4kDGgIBC4wDD4wD1AECAQeUAQ+UAVYDAYsBlQEPrAEpAwGEAa0BD7QBGAMBgAG1AQ+8ARgCAXy9AQ/pARcCAWXqAQ/xARgCAWHyAQ/yAa8CAgFa8wEP+gEYAgFVnAIRowIaAgFIpAIRqwIaAgFErAIRiAMWAgEMjAObAYwDvgECAQqMA8YBjAPRAQIBCJQBH5QBKQMBjAGVAaQClQG2AgMBiQGVAdkClQGdAwMBhwGVAcIClQHUAgMBhQGyATqyAYIBAwGDAa8BO68BQQMBgQG6ATm6AToCAX+3ATq3AUYCAX2+ARHoARwCAWm9AUq9AXkCAWbvATrvAYIBAgFk7AE77AFBAgFi8gGbAfIBmQICAV3yAaEC8gGsAgIBW/gBOfgBSAIBWPUBOvUBRQIBVqECPaECQQIBS54CPp4CSwIBSakCO6kCPAIBR6YCPKYCRwIBRawCH6wCOAIBDa4CFYMDIAIBFIYDFYYD4AECAQ+/ARO/AVoCAXrAARPXAS0CAXPYARPfARwCAW/gARPnARwCAWqvAhf7AiECAR78AheCAxwCARWGA6EBhgPKAQIBEoYD0gGGA90BAgEQvwEjvwEtAgF7wAGoAsABugICAXjAAd0CwAGhAwIBdsABxgLAAdgCAgF03QE+3QGGAQIBctoBP9oBRQIBcOUBPeUBTQIBbeIBPuIBSgIBa68CVq8CewIBQbACGfoCJAIBH/wCJfwCSwIBFv4CG4ADJgIBGbUCHeQCKAIBKewCIfYCLAIBIP8CHf8CvgICARq2Ah+2AmYCAT+3Ah++AigCATu/Ah+/As0BAgE2wAIf2wI5AgEv3AIf4wIoAgEq7QIj9AIsAgEl9QIj9QLbAQIBIf8CqQH/AqgCAgEd/wKwAv8CuwICARu2Ai+2AjkCAUC5Aku5AlUCATy/AqsBvwK3AQIBOb8CvwG/AsoBAgE3wAKkAsACtwICATTAAtkCwAKkAwIBMsACwwLAAtQCAgEw4QJJ4QJYAgEt3gJK3gJVAgEr8gJP8gJUAgEo7wJQ7wJdAgEm9QKvAfUCxQECAST1As0B9QLYAQIBIg==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="958,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="958,60" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="958,60" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="958,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="958,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="958,60" />
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="958,22" />
      <sap2010:ViewStateData Id="Delay_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_7" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Delay_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,408">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="464,566" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="464,60" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="486,1276">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DoWhile_1" sap:VirtualizedContainerService.HintSize="958,1438">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="958,22" />
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="711,60" />
      <sap2010:ViewStateData Id="Delay_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="264,470">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,243">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="404,21">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="418,697" />
      <sap2010:ViewStateData Id="DoWhile_2" sap:VirtualizedContainerService.HintSize="464,859">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,294" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="486,1317">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="711,1465" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="733,1789">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="958,1937">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="980,4263">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_1" sap:VirtualizedContainerService.HintSize="1010,4411" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="1032,4535">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1072,4615" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>