﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow" this:Workflow.tenantID="https://mingle-ionapi.inforcloudsuite.com/OLDCASTLE_DEV/" this:Workflow.vendorName="abcd" this:Workflow.vendorStr="174999;200774"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:this="clr-namespace:RehostedWorkflowDesigner"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="vendorID" Type="OutArgument(x:String)" />
    <x:Property Name="vendorName" Type="InArgument(x:String)" />
    <x:Property Name="vendorStr" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>System</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="VendorID_Sequence_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="vendorTextInput" />
      <Variable x:TypeArguments="njl:JToken" Name="vendorjsoninput" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="vendorresponseobj" />
      <Variable x:TypeArguments="s:String[]" Name="vendorResp1" />
    </Sequence.Variables>
    <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VendorID_TemplateApply_vendorTextInput_2" sap2010:WorkflowViewState.IdRef="Template_Apply_1" Template="{}{'data':[['vendorid_selector','vendorids','name','percentage','','']]}" Text="[vendorTextInput]">
      <ias:Template_Apply.Values>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>vendorName</x:String>
            <x:String>vendorStr</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>vendorName</x:String>
            <x:String>vendorStr</x:String>
          </scg:List>
        </scg:List>
      </ias:Template_Apply.Values>
    </ias:Template_Apply>
    <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VendorID_DeserializeJSON_vendorjsoninput_3" sap2010:WorkflowViewState.IdRef="DeserializeJSON_1" JTokenObject="[vendorjsoninput]" JTokenString="[vendorTextInput]" />
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" QueryParameters="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VendorID_IONAPIRequestWizard_vendorresponseobj_4" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" PostData="[vendorjsoninput.tostring]" Response="[vendorresponseobj]" Url="[tenantID + &quot;COLEMANAI/aiplatform/v1/endpoints/M3_IP_AI_Model/prediction&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
    </iai:IONAPIRequestWizard>
    <Assign DisplayName="VendorID_Assign_vendorID_5" sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[vendorresponseobj.ReadAsJson(0)(0).ToString]</InArgument>
      </Assign.Value>
    </Assign>
    <If Condition="[vendorID = &quot;No_Similarity&quot;]" DisplayName="VendorID_If_vendorIDNoSimilarity_6" sap2010:WorkflowViewState.IdRef="If_1">
      <If.Then>
        <Assign DisplayName="VendorID_Assign_vendorIDEmpty_7" sap2010:WorkflowViewState.IdRef="Assign_4">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[vendorID]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">
              <Literal x:TypeArguments="x:String" Value="" />
            </InArgument>
          </Assign.Value>
        </Assign>
      </If.Then>
    </If>
    <sads:DebugSymbol.Symbol>d1JDOlxVc2Vyc1xzaW5hbXB1ZGlcQXBwRGF0YVxMb2NhbFxJbmZvclJQQVxNM0ludm9pY2VQcm9jZXNzaW5nR2VuQUlWM1x2ZW5kb3JJRC54YW1sFgHCAQHHAQEEAW4BpwEBAwHhAQHvAQECQwN8DgIBAUoFVxoCARhYBVj3AQIBE1kFZB8CAQ1lBWwOAgEJbQV6CgIBAkqIAkqbAgIBGkq4AUqCAgIBGVjhAVj0AQIBFljAAVjTAQIBFFnKAlnmAgIBElnwAlmFAwIBEFmKA1ngAwIBDmowal0CAQxnMWc7AgEKbRNtOwIBA28JeBICAQV1D3U+AgEIcTVxPwIBBg==</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Template_Apply_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_4" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_1" sap:VirtualizedContainerService.HintSize="464,216" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="486,628">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="526,988" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>