﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iae="clr-namespace:Infor.Activities.Email;assembly=Infor.Activities.Email"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="poFilterValues" Type="InOutArgument(x:String)" />
    <x:Property Name="poFilterCondition" Type="InOutArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="invoiceFolderPath" Type="InArgument(x:String)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractNumericFromPO" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="processExpenseInvoice" Type="InArgument(x:Boolean)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System.IO</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ReadFilesFromFolder_Sequence_emails_1" sap2010:WorkflowViewState.IdRef="Sequence_1">
    <Sequence.Variables>
      <Variable x:TypeArguments="scg:List(iae:Mail)" Name="emails" />
      <Variable x:TypeArguments="x:String" Name="inProgressFolder" />
    </Sequence.Variables>
    <Sequence DisplayName="ReadFilesFromFolder_Sequence_inProgressFiles_2" sap2010:WorkflowViewState.IdRef="Sequence_3">
      <Sequence.Variables>
        <Variable x:TypeArguments="scg:List(x:String)" Name="inProgressFiles" />
        <Variable x:TypeArguments="scg:List(x:String)" Name="attachments" />
        <Variable x:TypeArguments="x:Boolean" Name="pdfFileAvailable" />
        <Variable x:TypeArguments="scg:List(x:String)" Name="files" />
      </Sequence.Variables>
      <Assign DisplayName="ReadFilesFromFolder_Assign_inProgressFolder_3" sap2010:WorkflowViewState.IdRef="Assign_1">
        <Assign.To>
          <OutArgument x:TypeArguments="x:String">[inProgressFolder]</OutArgument>
        </Assign.To>
        <Assign.Value>
          <InArgument x:TypeArguments="x:String">[configurationFolder+ "\InProgress"]</InArgument>
        </Assign.Value>
      </Assign>
      <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[inProgressFolder]" DisplayName="ReadFilesFromFolder_Directory_GetFiles_inProgressFiles_4" FileType="All" Files="[inProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_1" IncludeSubDir="True" />
      <ForEach x:TypeArguments="x:String" DisplayName="ReadFilesFromFolder_ForEach_file_5" sap2010:WorkflowViewState.IdRef="ForEach`1_3" Values="[inProgressFiles]">
        <ActivityAction x:TypeArguments="x:String">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="x:String" Name="file" />
          </ActivityAction.Argument>
          <ias:File_Delete ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ReadFilesFromFolder_File_Delete_file_6" sap2010:WorkflowViewState.IdRef="File_Delete_1" Source="[file]" />
        </ActivityAction>
      </ForEach>
      <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[invoiceFolderPath]" DisplayName="ReadFilesFromFolder_Directory_GetFiles_files_7" FileType="All" Files="[files]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_2" IncludeSubDir="False" />
      <ForEach x:TypeArguments="x:String" DisplayName="ReadFilesFromFolder_ForEach_file_8" sap2010:WorkflowViewState.IdRef="ForEach`1_7" Values="[files]">
        <ActivityAction x:TypeArguments="x:String">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="x:String" Name="file" />
          </ActivityAction.Argument>
          <Sequence DisplayName="ReadFilesFromFolder_Sequence_fileName_9" sap2010:WorkflowViewState.IdRef="Sequence_9">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="fileName" />
              <Variable x:TypeArguments="x:String" Name="outputFile" />
            </Sequence.Variables>
            <Assign DisplayName="ReadFilesFromFolder_Assign_fileName_10" sap2010:WorkflowViewState.IdRef="Assign_8">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[fileName]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[Path.GetFileName(file).replace(".PDF","_copy.pdf")]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ReadFilesFromFolder_File_Move_outputFile_11" sap2010:WorkflowViewState.IdRef="File_Move_2" OutputFile="[outputFile]" OverwriteFile="False" Source="[file]" Target="[inProgressFolder]" targetName="[fileName]" />
          </Sequence>
        </ActivityAction>
      </ForEach>
      <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[inProgressFolder]" DisplayName="ReadFilesFromFolder_Directory_GetFiles_inProgressFiles_12" FileType="All" Files="[inProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_3" IncludeSubDir="True" />
      <ForEach x:TypeArguments="x:String" DisplayName="ReadFilesFromFolder_ForEach_file_13" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[inProgressFiles]">
        <ActivityAction x:TypeArguments="x:String">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="x:String" Name="file" />
          </ActivityAction.Argument>
          <Sequence DisplayName="ReadFilesFromFolder_Sequence_copiedFile_14" sap2010:WorkflowViewState.IdRef="Sequence_5">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="copiedFile" />
            </Sequence.Variables>
            <If Condition="[file.contains(&quot;.PDF&quot;)]" DisplayName="ReadFilesFromFolder_If_file_15" sap2010:WorkflowViewState.IdRef="If_5">
              <If.Then>
                <Sequence DisplayName="ReadFilesFromFolder_Sequence_pdfFileName_16" sap2010:WorkflowViewState.IdRef="Sequence_7">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="pdfFileName" />
                    <Variable x:TypeArguments="x:String" Name="copiedFilePath" />
                  </Sequence.Variables>
                  <Assign DisplayName="ReadFilesFromFolder_Assign_pdfFileName_17" sap2010:WorkflowViewState.IdRef="Assign_5">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[pdfFileName]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[Path.GetFileName(file).replace(".PDF","_copy.pdf")]</InArgument>
                    </Assign.Value>
                  </Assign>
                  <ias:File_Copy ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ReadFilesFromFolder_File_Copy_copiedFilePath_18" sap2010:WorkflowViewState.IdRef="File_Copy_2" OutputFile="[copiedFilePath]" OverwriteFile="False" Source="[file]" Target="[inProgressFolder]" TargetFilename="[pdfFileName]" />
                  <Assign DisplayName="ReadFilesFromFolder_Assign_file_19" sap2010:WorkflowViewState.IdRef="Assign_6">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[file]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">[file.replace(".PDF","_copy.pdf")]</InArgument>
                    </Assign.Value>
                  </Assign>
                </Sequence>
              </If.Then>
            </If>
            <If Condition="[file.contains(&quot;.pdf&quot;)]" DisplayName="ReadFilesFromFolder_If_file_20" sap2010:WorkflowViewState.IdRef="If_2">
              <If.Then>
                <Sequence DisplayName="ReadFilesFromFolder_Sequence_fileName_21" sap2010:WorkflowViewState.IdRef="Sequence_4">
                  <Sequence.Variables>
                    <Variable x:TypeArguments="x:String" Name="fileName" />
                    <Variable x:TypeArguments="x:String" Name="outputFile" />
                  </Sequence.Variables>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ReadFilesFromFolder_Append_Line_logFile_22" sap2010:WorkflowViewState.IdRef="Append_Line_3" Source="[logFile]">
                    <ias:Append_Line.Line>
                      <InArgument x:TypeArguments="x:String">
                        <Literal x:TypeArguments="x:String" Value="" />
                      </InArgument>
                    </ias:Append_Line.Line>
                  </ias:Append_Line>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ReadFilesFromFolder_Append_Line_logFIle_23" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[&quot;Source : Configured Folder File Name : &quot;+file.Substring(file.LastIndexOf(&quot;\&quot;c)+1,(file.Length()-file.LastIndexOf(&quot;\&quot;c))-1)]" Source="[logFIle]" />
                  <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,file},{&quot;logFile&quot;,logFile},{&quot;configurationFolder&quot;,configurationFolder},{&quot;manualEntry&quot;,false},{&quot;emailSubject&quot;,&quot;ConfiguredFolder&quot;},{&quot;miscValues&quot;,miscValues},{&quot;emailReceivedTime&quot;,&quot;ConfiguredFolder&quot;},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;approvalRequired&quot;,approvalRequired},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice}}]" ContinueOnError="True" DisplayName="ReadFilesFromFolder_InvokeWorkflow_Arguments_24" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_1" WorkflowFile="[projectPath+&quot;\GetOCRValuesNew.xaml&quot;]" />
                </Sequence>
              </If.Then>
            </If>
          </Sequence>
        </ActivityAction>
      </ForEach>
    </Sequence>
    <sads:DebugSymbol.Symbol>d19DOlxVc2Vyc1xza2F2aXJheWFuaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXFJlYWRGaWxlc0Zyb21Gb2xkZXIueGFtbDpQA8EBDgIBAVUFvwEQAgECXAdjEAIBV2QHZIQCAgFSZQdsEQIBTG0HbfwBAgFHbgeDARECATaEAQeEAYQCAgExhQEHvgERAgEDYTJhVgIBWl4zXkUCAVhkogFktQECAVVkU2RnAgFTZYQBZZcBAgFQagtqpAECAU1towFtrAECAUptU21oAgFIboQBbo0BAgFFcwuBARYCATeEAaIBhAG1AQIBNIQBU4QBZwIBMoUBhAGFAZcBAgEvigELvAEWAgEEapkBaqEBAgFOeA1/FgIBQYABDYABhAICATiOAQ2oARICARmpAQ27ARICAQV9OH1sAgFEejl6QwIBQoABmQGAAacBAgE/gAHVAYAB6QECAT2AAfUBgAGBAgIBO4ABxQGAAc0BAgE5jgEbjgE+AgEakAERpgEcAgEcqQEbqQE+AgEGqwERuQEcAgEIlQETnAEcAgErnQETnQGVAgIBIp4BE6UBHAIBHbABE7YBJQIBFbcBE7cB0gICAQ24ARO4AfsMAgEJmgE+mgFyAgEulwE/lwFMAgEsnQGfAZ0BsQECASmdAd8BnQHzAQIBJ50BzwGdAdcBAgElnQGDAp0BkgICASOjAT6jAWACASCgAT+gAUUCAR6zARmzAUgCARiwAaEBsAGsAQIBFrcBnwG3AbwCAgEQtwHEArcBzwICAQ64AWS4AdELAgEMuAHHDLgB+AwCAQo=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_1" sap:VirtualizedContainerService.HintSize="516,60" />
      <sap2010:ViewStateData Id="Directory_GetFiles_1" sap:VirtualizedContainerService.HintSize="516,22" />
      <sap2010:ViewStateData Id="File_Delete_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="ForEach`1_3" sap:VirtualizedContainerService.HintSize="516,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_2" sap:VirtualizedContainerService.HintSize="516,22" />
      <sap2010:ViewStateData Id="Assign_8" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Move_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_9" sap:VirtualizedContainerService.HintSize="264,248">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_7" sap:VirtualizedContainerService.HintSize="516,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_3" sap:VirtualizedContainerService.HintSize="516,22" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="File_Copy_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_5" sap:VirtualizedContainerService.HintSize="464,494">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="222,270">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_2" sap:VirtualizedContainerService.HintSize="464,418" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="486,1076">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="516,1224" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="538,1816">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="560,1940">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="600,2060" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>