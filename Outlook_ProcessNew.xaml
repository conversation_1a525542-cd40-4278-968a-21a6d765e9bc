﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iae="clr-namespace:Infor.Activities.Email;assembly=Infor.Activities.Email"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iaw="clr-namespace:Infor.Activities.Workflow;assembly=Infor.Activities.Workflow"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="emailAccount" Type="InArgument(x:String)" />
    <x:Property Name="emailFolder" Type="InArgument(x:String)" />
    <x:Property Name="numberOfEmails" Type="InArgument(x:Int32)" />
    <x:Property Name="enableMessageBoxes" Type="InArgument(x:Boolean)" />
    <x:Property Name="logFile" Type="InArgument(x:String)" />
    <x:Property Name="invoiceSource" Type="InArgument(x:String)" />
    <x:Property Name="configurationFolder" Type="InArgument(x:String)" />
    <x:Property Name="logFolderName" Type="InArgument(x:String)" />
    <x:Property Name="directoriesNames" Type="InArgument(x:String)" />
    <x:Property Name="projectPath" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="colemanAPI" Type="InArgument(x:String)" />
    <x:Property Name="userIdentifier" Type="InArgument(x:String)" />
    <x:Property Name="distributionType" Type="InArgument(x:String)" />
    <x:Property Name="poFilterValues" Type="InArgument(x:String)" />
    <x:Property Name="poFilterCondition" Type="InArgument(x:String)" />
    <x:Property Name="datalakeAPILogicalId" Type="InArgument(x:String)" />
    <x:Property Name="chargeCode" Type="InArgument(x:String)" />
    <x:Property Name="discountCode" Type="InArgument(x:String)" />
    <x:Property Name="authUser" Type="InArgument(x:String)" />
    <x:Property Name="imsAPIUrl" Type="InArgument(x:String)" />
    <x:Property Name="poDiscountsHandlingConfig" Type="InArgument(x:Boolean)" />
    <x:Property Name="vatCodeConfig" Type="InArgument(x:String)" />
    <x:Property Name="miscValues" Type="InArgument(scg:Dictionary(x:String, x:Object))" />
    <x:Property Name="processExpenseInvoice" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalRequired" Type="InArgument(x:Boolean)" />
    <x:Property Name="matchVendorItemCode" Type="InArgument(x:Boolean)" />
    <x:Property Name="approvalWorkflow" Type="InArgument(x:String)" />
    <x:Property Name="extractNumericFromPO" Type="InArgument(x:Boolean)" />
    <x:Property Name="extractFromWidgetDatalake" Type="InArgument(x:Boolean)" />
    <x:Property Name="checkAmountBussinessRule" Type="InArgument(x:String)" />
    <x:Property Name="InProgressFolder" Type="InArgument(x:String)" />
    <x:Property Name="MasterDownloads" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>System.Linq</x:String>
      <x:String>System.IO</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Collections.Immutable</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <TryCatch DisplayName="TryCatch - Process Outlook Emails" sap2010:WorkflowViewState.IdRef="TryCatch_5">
    <TryCatch.Variables>
      <Variable x:TypeArguments="scg:List(x:String)" Name="inProgressFiles" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="processPoResp" />
      <Variable x:TypeArguments="x:Int32" Name="processIdCount" />
      <Variable x:TypeArguments="x:Int32" Name="statsCount" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Default="[New List(Of List(Of String))]" Name="mainListNew" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects1" />
      <Variable x:TypeArguments="x:String" Name="strMoveFile" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Name="approvalLists" />
      <Variable x:TypeArguments="scg:List(scg:List(x:String))" Default="[New List(Of List(Of String))]" Name="finalizeStoreStat" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects2" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects3" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects4" />
      <Variable x:TypeArguments="scg:IDictionary(x:String, x:Object)" Name="emailsubjects5" />
    </TryCatch.Variables>
    <TryCatch.Try>
      <Sequence DisplayName="Process Outlook Emails Sequence" sap2010:WorkflowViewState.IdRef="Sequence_72">
        <Assign sap2010:WorkflowViewState.IdRef="Assign_82">
          <Assign.To>
            <OutArgument x:TypeArguments="x:String">[InProgressFolder]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:String">[configurationFolder+ "\InProgress"]</InArgument>
          </Assign.Value>
        </Assign>
        <Sequence DisplayName="Process Outlook Emails" sap2010:WorkflowViewState.IdRef="Sequence_67">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(iae:Mail)" Default="[new List( Of Mail)]" Name="ListAllemails" />
            <Variable x:TypeArguments="x:Int32" Name="ln" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM1" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM2" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM3" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM4" />
            <Variable x:TypeArguments="scg:List(iae:Mail)" Name="SM5" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects2" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects3" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects4" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects5" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[new list ( of string)]" Name="tsubjects1" />
            <Variable x:TypeArguments="x:String" Name="p1_time" />
            <Variable x:TypeArguments="x:String" Name="p2_time" />
            <Variable x:TypeArguments="x:String" Name="p3_time" />
            <Variable x:TypeArguments="x:String" Name="p4_time" />
            <Variable x:TypeArguments="x:String" Name="p5_time" />
            <Variable x:TypeArguments="x:Int32" Default="5" Name="numberOfParts" />
            <Variable x:TypeArguments="x:Int32" Name="emailResponseCode" />
            <Variable x:TypeArguments="x:String" Name="strMasterFailureData" />
          </Sequence.Variables>
          <Switch x:TypeArguments="x:String" DisplayName="Read Outlook Emails as Per invoiceSource" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_17">
            <iae:GetOutlookEmails Filter="{x:Null}" x:Key="OutlookClientEmail" Account="[emailAccount.Trim]" ContinueOnError="False" DisplayName="Get Outlook Emails" Emails="[ListAllemails]" ErrorCode="[emailResponseCode]" Folder="[emailFolder.Trim]" sap2010:WorkflowViewState.IdRef="GetOutlookEmails_9" MarkAsRead="True" OnlyUnread="True" TopEmails="[numberOfEmails]" />
            <x:Null x:Key="OutlookGraphEmail" />
          </Switch>
          <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_17">
            <iad:CommentOut.Activities>
              <DoWhile DisplayName="Read all Outlook unread emails" sap2010:WorkflowViewState.IdRef="DoWhile_3">
                <DoWhile.Variables>
                  <Variable x:TypeArguments="iae:Mail" Name="mail" />
                  <Variable x:TypeArguments="x:Int32" Name="TotalNoOfEmails" />
                </DoWhile.Variables>
                <DoWhile.Condition>[emails.count&gt;0]</DoWhile.Condition>
                <Sequence DisplayName="Outlook Sequence" sap2010:WorkflowViewState.IdRef="Sequence_60">
                  <Switch x:TypeArguments="x:String" DisplayName="Read Outlook Emails as Per Source" Expression="[invoiceSource]" sap2010:WorkflowViewState.IdRef="Switch`1_4">
                    <iae:GetOutlookEmails Filter="{x:Null}" x:Key="OutlookClientEmail" Account="[emailAccount]" ContinueOnError="False" DisplayName="Get Outlook Emails" Emails="[emails]" ErrorCode="[emailResponseCode]" Folder="[emailFolder]" sap2010:WorkflowViewState.IdRef="GetOutlookEmails_5" MarkAsRead="True" OnlyUnread="True" TopEmails="[numberOfEmails]" />
                    <x:Null x:Key="OutlookGraphEmail" />
                  </Switch>
                  <Switch x:TypeArguments="x:Boolean" DisplayName="If Emails count is &gt;0" Expression="[emails.count&gt;0]" sap2010:WorkflowViewState.IdRef="Switch`1_5">
                    <ForEach x:TypeArguments="iae:Mail" x:Key="True" DisplayName="Build ListAllEmails" sap2010:WorkflowViewState.IdRef="ForEach`1_5" Values="[emails]">
                      <ActivityAction x:TypeArguments="iae:Mail">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="iae:Mail" Name="mail" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="iae:Mail">[mail]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                  </Switch>
                </Sequence>
              </DoWhile>
            </iad:CommentOut.Activities>
          </iad:CommentOut>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_42" Line="[Environment.newLine+&quot;Outlook emails extraction is done and the total mail count is - &quot;+ListAllemails.count.tostring+Environment.newLine+&quot;Emails Response Status code : &quot;+emailResponseCode.tostring]" Source="[logfile]" />
          <Sequence DisplayName="Create Lists for 5 sub ListMails processes" sap2010:WorkflowViewState.IdRef="Sequence_61">
            <Assign sap2010:WorkflowViewState.IdRef="Assign_66">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[ln]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[cint(ListAllemails.Count/ numberOfParts)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_67">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM1]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(1 - 1).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_68">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM2]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(1*ln).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_69">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM3]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(2*ln).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_70">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM4]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(3*ln).Take(ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_71">
              <Assign.To>
                <OutArgument x:TypeArguments="scg:List(iae:Mail)">[SM5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="scg:List(iae:Mail)">[ListAllemails.Skip(4*ln).ToList()]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_56" Line="[&quot;Count of Mails in 5 sub mail lists is &quot;+Environment.NewLine+&quot;SM1 - &quot;+SM1.Count.tostring+Environment.NewLine+&quot;SM2 - &quot;+SM2.Count.tostring+Environment.NewLine+&quot;SM3 - &quot;+SM3.Count.tostring+Environment.NewLine+&quot;SM4 - &quot;+SM4.Count.tostring+Environment.NewLine+&quot;SM5 - &quot;+SM5.Count.tostring+Environment.NewLine+&quot;Total Emails: &quot;  +ListAllemails.count.tostring]" Source="[logfile]" />
          <Switch x:TypeArguments="x:Boolean" DisplayName="Initiate Parallel Processing if Mails Available" Expression="[ListAllemails.count&gt;0]" sap2010:WorkflowViewState.IdRef="Switch`1_6">
            <Parallel x:Key="True" DisplayName="Parallel Sequence Updated" sap2010:WorkflowViewState.IdRef="Parallel_6">
              <If Condition="[SM1.Count &gt; 0]" DisplayName="Parallel Sequence--1" sap2010:WorkflowViewState.IdRef="If_35">
                <If.Then>
                  <Sequence DisplayName="Sequence 1" sap2010:WorkflowViewState.IdRef="Sequence_121">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_202">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p1_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM1},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;1&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_A&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke preprocess 1" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_52" OutputArguments="[emailsubjects1]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_203">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects1("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_10" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_9" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_204">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects1("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM1 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_122">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_205">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p1_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["No Data to process in Parallel Sequence 1."]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_206">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects1]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
              <If Condition="[SM2.Count &gt; 0]" DisplayName="Parallel Sequence--2" sap2010:WorkflowViewState.IdRef="If_36">
                <If.Then>
                  <Sequence DisplayName="Sequence 2" sap2010:WorkflowViewState.IdRef="Sequence_123">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_207">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p2_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM2},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;2&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_B&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke Workflow 2" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_53" OutputArguments="[emailsubjects2]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_208">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects2("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_11" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_10" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_209">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects2("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM2 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_124">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_210">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p2_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">No Data to process in Parallel Sequence 2.</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_211">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects2]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
              <If Condition="[SM3.Count &gt; 0]" DisplayName="Parallel Sequence--3" sap2010:WorkflowViewState.IdRef="If_37">
                <If.Then>
                  <Sequence DisplayName="Sequence 3" sap2010:WorkflowViewState.IdRef="Sequence_125">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_212">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p3_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM3},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;3&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_C&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke Workflow 3" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_54" OutputArguments="[emailsubjects3]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_213">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects3("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_12" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_11" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_214">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects3]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects3("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM3 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_126">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_215">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p3_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">No Data to process in Parallel Sequence 3.</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_216">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects3]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
              <If Condition="[SM4.Count &gt; 0]" DisplayName="Parallel Sequence--4" sap2010:WorkflowViewState.IdRef="If_38">
                <If.Then>
                  <Sequence DisplayName="Sequence 4" sap2010:WorkflowViewState.IdRef="Sequence_127">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_217">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p4_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM4},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;4&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_D&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke Workflow 4" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_55" OutputArguments="[emailsubjects4]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_218">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects4("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_13" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_12" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_219">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects4]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects4("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM4 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_128">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_220">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p4_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">No Data to process in Parallel Sequence 4.</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_221">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects4]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
              <If Condition="[SM5.Count &gt; 0]" DisplayName="Parallel Sequence--5" sap2010:WorkflowViewState.IdRef="If_39">
                <If.Then>
                  <Sequence DisplayName="Sequence 5" sap2010:WorkflowViewState.IdRef="Sequence_129">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_222">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p5_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">["start time: "+Environment.newLine+System.DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss")]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <iaw:InvokeWorkflow ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;submails&quot;,SM5},{&quot;configurationFolder&quot;,configurationFolder},{&quot;logfile&quot;,logfile},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;miscValues&quot;,miscValues},{&quot;invoiceSource&quot;,invoiceSource},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;seqnumber&quot;,&quot;5&quot;},{&quot;DownloadTo&quot;,&quot;DownloadedFiles_&quot;},{&quot;FileRenameFormat&quot;,&quot;InforOS_E&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;emailAccount&quot;,emailAccount}}]" ContinueOnError="False" DisplayName="Invoke Workflow 5" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_56" OutputArguments="[emailsubjects5]" WorkflowFile="[projectPath+&quot;\Outlook_preprocess.xaml&quot;]" />
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_223">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[mainListNew]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[CType(emailsubjects5("mainStoreStats"), List(Of List(Of String)))]</InArgument>
                      </Assign.Value>
                    </Assign>
                    <ForEach x:TypeArguments="scg:List(x:String)" DisplayName="ForEach&lt;List&lt;String&gt;&gt;" sap2010:WorkflowViewState.IdRef="ForEach`1_14" Values="[mainListNew]">
                      <ActivityAction x:TypeArguments="scg:List(x:String)">
                        <ActivityAction.Argument>
                          <DelegateInArgument x:TypeArguments="scg:List(x:String)" Name="item" />
                        </ActivityAction.Argument>
                        <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_13" MethodName="Add">
                          <InvokeMethod.TargetObject>
                            <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[finalizeStoreStat]</InArgument>
                          </InvokeMethod.TargetObject>
                          <InArgument x:TypeArguments="scg:List(x:String)">[item]</InArgument>
                        </InvokeMethod>
                      </ActivityAction>
                    </ForEach>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_224">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects5]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[TryCast(emailsubjects5("emailsubjects"),List(Of String))]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Then>
                <If.Else>
                  <Sequence DisplayName="No Mails in SM5 Mail list" sap2010:WorkflowViewState.IdRef="Sequence_130">
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_225">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[p5_time]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">No Data to process in Parallel Sequence 5.</InArgument>
                      </Assign.Value>
                    </Assign>
                    <Assign sap2010:WorkflowViewState.IdRef="Assign_226">
                      <Assign.To>
                        <OutArgument x:TypeArguments="scg:List(x:String)">[tsubjects5]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                      </Assign.Value>
                    </Assign>
                  </Sequence>
                </If.Else>
              </If>
            </Parallel>
            <Throw x:Key="False" DisplayName="No Mail Data Throw" Exception="[New SystemException(&quot;No unread emails in the mailbox to process.&quot;)]" sap2010:WorkflowViewState.IdRef="Throw_2" />
          </Switch>
          <If Condition="[(&#xA;  emailsubjects1 IsNot Nothing AndAlso emailsubjects1(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects1(&quot;blnFailureExists&quot;), Boolean)&#xA;) OrElse (&#xA;  emailsubjects2 IsNot Nothing AndAlso emailsubjects2(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects2(&quot;blnFailureExists&quot;), Boolean)&#xA;) OrElse (&#xA;  emailsubjects3 IsNot Nothing AndAlso emailsubjects3(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects3(&quot;blnFailureExists&quot;), Boolean)&#xA;) OrElse (&#xA;  emailsubjects4 IsNot Nothing AndAlso emailsubjects4(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects4(&quot;blnFailureExists&quot;), Boolean)&#xA;) OrElse (&#xA;  emailsubjects5 IsNot Nothing AndAlso emailsubjects5(&quot;blnFailureExists&quot;) IsNot Nothing AndAlso CType(emailsubjects5(&quot;blnFailureExists&quot;), Boolean)&#xA;)]" DisplayName="Notify if any Failure emails" sap2010:WorkflowViewState.IdRef="If_40">
            <If.Then>
              <Sequence DisplayName="Failure Mails Notification Sequence" sap2010:WorkflowViewState.IdRef="Sequence_131">
                <Sequence.Variables>
                  <Variable x:TypeArguments="x:String" Name="notificationString" />
                  <Variable x:TypeArguments="njl:JToken" Name="notificationToken" />
                  <Variable x:TypeArguments="iru:ResponseObject" Name="notificationResponse" />
                  <Variable x:TypeArguments="x:String" Name="strMessageTitle" />
                  <Variable x:TypeArguments="x:String" Name="strUserIdentifier" />
                  <Variable x:TypeArguments="x:String" Name="strDistributionType" />
                </Sequence.Variables>
                <Assign DisplayName="Assign UserIdentifier" sap2010:WorkflowViewState.IdRef="Assign_232">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strUserIdentifier]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[miscValues("userIdentifier").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="Assign strDistributionType" sap2010:WorkflowViewState.IdRef="Assign_233">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strDistributionType]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[miscValues("distributionType").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_231">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strMessageTitle]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["📣 M3 Invoice Processing Failure Summary " &amp; miscValues("StartTime").Tostring]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign sap2010:WorkflowViewState.IdRef="Assign_235">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[strMasterFailureData]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String" xml:space="preserve">[String.Join(","c,
    {
        If(emailsubjects1 IsNot Nothing AndAlso emailsubjects1.ContainsKey("strJsonString"), CStr(emailsubjects1("strJsonString")), ""),
        If(emailsubjects2 IsNot Nothing AndAlso emailsubjects2.ContainsKey("strJsonString"), CStr(emailsubjects2("strJsonString")), ""),
        If(emailsubjects3 IsNot Nothing AndAlso emailsubjects3.ContainsKey("strJsonString"), CStr(emailsubjects3("strJsonString")), ""),
        If(emailsubjects4 IsNot Nothing AndAlso emailsubjects4.ContainsKey("strJsonString"), CStr(emailsubjects4("strJsonString")), ""),
        If(emailsubjects5 IsNot Nothing AndAlso emailsubjects5.ContainsKey("strJsonString"), CStr(emailsubjects5("strJsonString")), "")
    }.Where(Function(x) Not String.IsNullOrWhiteSpace(x)).ToArray())]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Templating Activity" sap2010:WorkflowViewState.IdRef="Template_Apply_4" Template="{}{ &#xA;  'message': '{{%strMessageTitle%}}',&#xA;  'category': 'RPA', &#xA;  'parameters': [&#xA;    {{%strMasterFailureData%}}&#xA;  ], &#xA;  'distribution': [&#xA;    {&#xA;      'identifier': '{{%userIdentifier%}}',&#xA;      'type': '{{%distributionType%}}',&#xA;      'sendMail': '{{%SendEmail%}}' &#xA;    }&#xA;  ]&#xA;}" Text="[notificationString]">
                  <ias:Template_Apply.Values>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>strMasterFailureData</x:String>
                        <x:String>userIdentifier</x:String>
                        <x:String>distributionType</x:String>
                        <x:String>SendEmail</x:String>
                        <x:String>strMessageTitle</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>strMasterFailureData</x:String>
                        <x:String>strUserIdentifier</x:String>
                        <x:String>strDistributionType</x:String>
                        <x:String>TRUE</x:String>
                        <x:String>strMessageTitle</x:String>
                      </scg:List>
                    </scg:List>
                  </ias:Template_Apply.Values>
                </ias:Template_Apply>
                <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Deserialize JSON" sap2010:WorkflowViewState.IdRef="DeserializeJSON_3" JTokenObject="[notificationToken]" JTokenString="[notificationString]" />
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="IONAPI Request" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_3" PostData="[notificationToken.tostring]" Response="[notificationResponse]" Url="[tenantID+ &quot;IONSERVICES/process/application/v1/pulse/notification/create&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>logicalId</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>lid://infor.rpa.1</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </Sequence>
            </If.Then>
          </If>
          <Sequence DisplayName="Assign Parallel Log Data" sap2010:WorkflowViewState.IdRef="Sequence_90">
            <Sequence.Variables>
              <Variable x:TypeArguments="x:String" Name="log1" />
              <Variable x:TypeArguments="x:String" Name="log2" />
              <Variable x:TypeArguments="x:String" Name="log3" />
              <Variable x:TypeArguments="x:String" Name="log4" />
              <Variable x:TypeArguments="x:String" Name="log5" />
            </Sequence.Variables>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_105">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log1]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p1_time) OrElse tsubjects1.Count &gt; 0,
   "process-1" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p1_time), "", p1_time) &amp; Environment.NewLine &amp;
   If(tsubjects1.Count = 0, "", String.Join(", ", tsubjects1)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_106">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log2]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p2_time) OrElse tsubjects2.Count &gt; 0,
   "process-2" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p2_time), "", p2_time) &amp; Environment.NewLine &amp;
   If(tsubjects2.Count = 0, "", String.Join(", ", tsubjects2)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_107">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log3]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p3_time) OrElse tsubjects3.Count &gt; 0,
   "process-3" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p3_time), "", p3_time) &amp; Environment.NewLine &amp;
   If(tsubjects3.Count = 0, "", String.Join(", ", tsubjects3)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_108">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log4]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p4_time) OrElse tsubjects4.Count &gt; 0,
   "process-4" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p4_time), "", p4_time) &amp; Environment.NewLine &amp;
   If(tsubjects4.Count = 0, "", String.Join(", ", tsubjects4)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign sap2010:WorkflowViewState.IdRef="Assign_109">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[log5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String" xml:space="preserve">[If(Not String.IsNullOrWhiteSpace(p5_time) OrElse tsubjects5.Count &gt; 0,
   "process-5" &amp; Environment.NewLine &amp;
   If(String.IsNullOrWhiteSpace(p5_time), "", p5_time) &amp; Environment.NewLine &amp;
   If(tsubjects5.Count = 0, "", String.Join(", ", tsubjects5)) &amp; Environment.NewLine,
   "")]</InArgument>
              </Assign.Value>
            </Assign>
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Append Line post Parallel Process" sap2010:WorkflowViewState.IdRef="Append_Line_57" Line="[&quot;Log data From all 5 parallel sequences is as below.&quot; &amp; Environment.NewLine &amp;&#xA;log1 &amp; log2 &amp; log3 &amp; log4 &amp; log5]" Source="[logfile]" />
            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Process Outlook Emails Completed  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_59" Line="Downloading attachments from Outlook Completed." Source="[logfile]" />
          </Sequence>
        </Sequence>
        <Sequence DisplayName="Split Sequence" sap2010:WorkflowViewState.IdRef="Sequence_89">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(x:String)" Name="MasterDownloadedFiles" />
          </Sequence.Variables>
          <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[MasterDownloads]" DisplayName="Get Files in OutlookDownloads" FileType="All" Files="[MasterDownloadedFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_3" IncludeSubDir="True" />
          <If Condition="[MasterDownloadedFiles.count&gt;0]" DisplayName="Check if any files in Master Downloads" sap2010:WorkflowViewState.IdRef="If_41">
            <If.Then>
              <Sequence DisplayName=" Files exist in Master Downloads" sap2010:WorkflowViewState.IdRef="Sequence_132">
                <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From{{&quot;logFile&quot;,logFile},{&quot;configurationFolder&quot;,configurationFolder},{&quot;tenantID&quot;,tenantID},{&quot;projectPath&quot;,projectPath},{&quot;miscValues&quot;,miscValues},{&quot;promptPath&quot;,ConfigurationFolder + &quot;\Classification.txt&quot;},{&quot;promptPath2&quot;,ConfigurationFolder + &quot;\GenAISplit2New.txt&quot;},{&quot;InProgressFolder&quot;,InProgressFolder},{&quot;strClassificationExtension&quot;,ConfigurationFolder + &quot;\GenAISplit1_Ext.txt&quot;},{&quot;MasterDownloads&quot;,MasterDownloads},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;strSplitExtension&quot;,ConfigurationFolder + &quot;\GenAISplit2_Ext.txt&quot;}}]" ContinueOnError="False" DisplayName="Invoke Classification_Split Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_48" WorkflowFile="[projectPath+&quot;\Classification_Split.xaml&quot;]" />
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification Split Sequence Completed  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_61" Line="[&quot;Classification (Split - +&quot;+miscValues(&quot;SplittingDoc&quot;).ToString+&quot;) Sequence Completed.&quot;]" Source="[logfile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Classification Split Sequence Completed  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_71" Line="No files available to process in Master Downlaods. Please check email notification for any failire emails." Source="[logfile]" />
            </If.Else>
          </If>
        </Sequence>
        <Sequence DisplayName="OCR Sequence" sap2010:WorkflowViewState.IdRef="Sequence_69">
          <Sequence.Variables>
            <Variable x:TypeArguments="scg:List(x:String)" Name="listInProgressFiles" />
            <Variable x:TypeArguments="x:String" Name="strSpecificText" />
            <Variable x:TypeArguments="scg:List(x:String)" Default="[New List(of string)]" Name="processIdLst" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="ListReProcess" />
            <Variable x:TypeArguments="scg:List(x:String)" Name="ListInProgressNames" />
            <Variable x:TypeArguments="x:String" Name="InvoiceFileName" />
            <Variable x:TypeArguments="x:Int32" Name="RenameCounter" />
            <Variable x:TypeArguments="x:Boolean" Name="blnReprocessExists" />
            <Variable x:TypeArguments="x:Int32" Name="processPoRespCode" />
          </Sequence.Variables>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="OCR Sequence Started  Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_62" Line="Get OCR Sequence Started." Source="[logfile]" />
          <Assign sap2010:WorkflowViewState.IdRef="Assign_198">
            <Assign.To>
              <OutArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[New List(Of List(Of String))]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Path_Validate ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Path Exists" sap2010:WorkflowViewState.IdRef="Path_Validate_1" IsValid="[blnReprocessExists]" Path="[configurationFolder+ &quot;\ReProcess&quot;]" />
          <Switch x:TypeArguments="x:Boolean" DisplayName="Check if Reprocess folder exists" Expression="[blnReprocessExists]" sap2010:WorkflowViewState.IdRef="Switch`1_16">
            <Sequence x:Key="True" DisplayName="Move Reprocess Files Sequence" sap2010:WorkflowViewState.IdRef="Sequence_114">
              <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[inProgressFolder]" DisplayName="Get Files from Inprogress" FileType="PDF" Files="[listInProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_1" IncludeSubDir="True" />
              <Assign sap2010:WorkflowViewState.IdRef="Assign_196">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:Int32">1</InArgument>
                </Assign.Value>
              </Assign>
              <Assign sap2010:WorkflowViewState.IdRef="Assign_194">
                <Assign.To>
                  <OutArgument x:TypeArguments="scg:List(x:String)">[ListInProgressNames]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="scg:List(x:String)">[new list( of string)]</InArgument>
                </Assign.Value>
              </Assign>
              <ForEach x:TypeArguments="x:String" DisplayName="Get FileNames from Inprogress Folder" sap2010:WorkflowViewState.IdRef="ForEach`1_9" Values="[listInProgressFiles]">
                <ActivityAction x:TypeArguments="x:String">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:String" Name="itemA" />
                  </ActivityAction.Argument>
                  <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                    <InvokeMethod.TargetObject>
                      <InArgument x:TypeArguments="scg:List(x:String)">[ListInProgressNames]</InArgument>
                    </InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="x:String">[Path.GetFileName(itemA).Tostring]</InArgument>
                  </InvokeMethod>
                </ActivityAction>
              </ForEach>
              <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="True" DirPath="[configurationFolder+ &quot;\ReProcess&quot;]" DisplayName="Get Reprocess Files in Directory" FileType="All" Files="[ListReProcess]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_10" IncludeSubDir="True" />
              <If Condition="[ListReProcess.Count&gt;0]" DisplayName="Check Reprocess files count and Move to Inprogress" sap2010:WorkflowViewState.IdRef="If_28">
                <If.Then>
                  <ForEach x:TypeArguments="x:String" DisplayName="Move all ReProcess Files to Inprogress" sap2010:WorkflowViewState.IdRef="ForEach`1_8" Values="[ListReProcess]">
                    <ActivityAction x:TypeArguments="x:String">
                      <ActivityAction.Argument>
                        <DelegateInArgument x:TypeArguments="x:String" Name="ReprocessFile" />
                      </ActivityAction.Argument>
                      <Sequence sap2010:WorkflowViewState.IdRef="Sequence_113">
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_188">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:String">[Path.GetFilename(ReprocessFile)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <Switch x:TypeArguments="x:Boolean" DisplayName="Move Files to InProgress Folder" Expression="[ListInProgressNames.Contains(Path.GetFileName(ReprocessFile))]" sap2010:WorkflowViewState.IdRef="Switch`1_15">
                          <Sequence x:Key="True" DisplayName="File Name Already Present in Master Downloads" sap2010:WorkflowViewState.IdRef="Sequence_111">
                            <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_29">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_189">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <ias:File_Move ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_3" OutputFile="[strMoveFile]" OverwriteFile="False" Source="[ReprocessFile]" Target="[configurationFolder+ &quot;\InProgress&quot;]" targetName="[InvoiceFileName.Replace(&quot;.pdf&quot;,&quot;_&quot;+RenameCounter.Tostring+&quot;.pdf&quot;)]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_195">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                          <Sequence x:Key="False" DisplayName="File Name not Already Present in Master Downloads Folder" sap2010:WorkflowViewState.IdRef="Sequence_112">
                            <If Condition="[System.Text.RegularExpressions.Regex.IsMatch(InvoiceFileName, &quot;[:#&amp;@`'*/\?&lt;&gt;|&quot;&quot;]&quot;)]" DisplayName="Check if the FileName Contains the special Characters" sap2010:WorkflowViewState.IdRef="If_30">
                              <If.Then>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_191">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[InvoiceFileName]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String">[System.Text.RegularExpressions.Regex.Replace(InvoiceFileName, "[:#&amp;@~`'*/\?&lt;&gt;|""]", "_")]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </If.Then>
                            </If>
                            <ias:File_Move ErrorCode="{x:Null}" targetName="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_5" OutputFile="[strMoveFile]" OverwriteFile="False" Source="[ReprocessFile]" Target="[configurationFolder+ &quot;\InProgress&quot;]" />
                          </Sequence>
                        </Switch>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_193">
                          <Assign.To>
                            <OutArgument x:TypeArguments="x:Int32">[RenameCounter]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="x:Int32">[RenameCounter+1]</InArgument>
                          </Assign.Value>
                        </Assign>
                      </Sequence>
                    </ActivityAction>
                  </ForEach>
                </If.Then>
              </If>
            </Sequence>
            <ias:Append_Line ErrorCode="{x:Null}" x:Key="False" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_67" Line="ReProcess folder doesn't exist." Source="[logFile]" />
          </Switch>
          <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[inProgressFolder]" DisplayName="Get Files from Inprogress" FileType="PDF" Files="[listInProgressFiles]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_12" IncludeSubDir="True" />
          <If Condition="[listInProgressFiles.count&gt;0]" DisplayName="Check if any files in In progress folder" sap2010:WorkflowViewState.IdRef="If_42">
            <If.Then>
              <ForEach x:TypeArguments="x:String" DisplayName="Process Each file in Inprogress with GetOCRValues XAML" sap2010:WorkflowViewState.IdRef="ForEach`1_6" Values="[listInProgressFiles]">
                <ActivityAction x:TypeArguments="x:String">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="x:String" Name="item2" />
                  </ActivityAction.Argument>
                  <TryCatch DisplayName="TryCatch Each file in Inprogress" sap2010:WorkflowViewState.IdRef="TryCatch_7">
                    <TryCatch.Try>
                      <Sequence DisplayName="Each file in Inprogress Sequence" sap2010:WorkflowViewState.IdRef="Sequence_71">
                        <Sequence.Variables>
                          <Variable x:TypeArguments="x:String" Name="CurrentOCRFile" />
                          <Variable x:TypeArguments="x:String" Name="OCRTextOut" />
                          <Variable x:TypeArguments="njl:JToken" Name="ReqOCRvalues" />
                          <Variable x:TypeArguments="x:String" Name="targetPages" />
                          <Variable x:TypeArguments="scg:List(x:String)" Name="approvalList" />
                        </Sequence.Variables>
                        <Assign sap2010:WorkflowViewState.IdRef="Assign_199">
                          <Assign.To>
                            <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                          </Assign.To>
                          <Assign.Value>
                            <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)]</InArgument>
                          </Assign.Value>
                        </Assign>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_52" Line="[&quot;File Name : &quot;+item2.Substring(item2.LastIndexOf(&quot;\&quot;c)+1,(item2.Length()-item2.LastIndexOf(&quot;\&quot;c))-1)]" Source="[logFile]" />
                        <Switch x:TypeArguments="x:Boolean" DisplayName="Check if SplittingDoc is True to send Specific OCR Text" Expression="[miscValues(&quot;SplittingDoc&quot;).ToString.ToLower = &quot;true&quot;]" sap2010:WorkflowViewState.IdRef="Switch`1_14">
                          <Sequence x:Key="True" DisplayName="Get OCR Text Rquired for Prompt" sap2010:WorkflowViewState.IdRef="Sequence_92">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:Int32" Name="indexTilde" />
                              <Variable x:TypeArguments="x:Int32" Name="indexLastDashBeforeTilde" />
                              <Variable x:TypeArguments="x:String" Name="SubFileName" />
                              <Variable x:TypeArguments="scg:List(x:String)" Name="ListOCRData" />
                              <Variable x:TypeArguments="s:String[]" Name="ArrayTargetPages" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_110">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[indexTilde]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[Path.GetfileName(item2).IndexOf("~"c)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_111">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:Int32">[indexLastDashBeforeTilde]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:Int32">[Path.GetfilenaMe(item2).LastIndexOf("{"c, indexTilde)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_112">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Path.GetfilenaMe(item2).Substring(0, indexLastDashBeforeTilde)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_113">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[SubFileName+".txt"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_175">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[If(SubFileName.EndsWith("-.txt"), SubFileName.Replace("-.txt", ".txt"), SubFileName)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Get OCR Files in OCR Data" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_4" IncludeSubDir="True" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_114">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[ListOCRData.FirstOrDefault(Function(p) Path.GetFileName(p).Trim().ToLower() = SubFileName.Trim().ToLower())]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_1" Source="[CurrentOCRFile]" Text="[OCRTextOut]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_116">
                              <Assign.To>
                                <OutArgument x:TypeArguments="njl:JToken">[ReqOCRvalues]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRTextOut)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_117">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[targetPages]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Path.GetfileName(item2).Split("~"c)(2)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_118">
                              <Assign.To>
                                <OutArgument x:TypeArguments="s:String[]">[ArrayTargetPages]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="s:String[]">[targetPages.Split(","c).Select(Function(x) x.Trim()).ToArray()]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_119">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String" xml:space="preserve">[JsonConvert.SerializeObject(
    ReqOCRvalues("data").
        Where(Function(p) ArrayTargetPages.Contains(p("PageNo").ToString())).
        Select(Function(p) New With {
            Key .OCR_text = p("OCR_text").ToString(),
            Key .PageNo = p("PageNo").ToString()
        })
)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_13">
                              <iad:CommentOut.Activities>
                                <Assign sap2010:WorkflowViewState.IdRef="Assign_201">
                                  <Assign.To>
                                    <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                                  </Assign.To>
                                  <Assign.Value>
                                    <InArgument x:TypeArguments="x:String" xml:space="preserve">[String.Join(Environment.NewLine,
  ReqOCRvalues("data").
    Where(Function(p) ArrayTargetPages.Contains(p("PageNo").ToString())).
    Select(Function(p) p("OCR_text").ToString()))]</InArgument>
                                  </Assign.Value>
                                </Assign>
                              </iad:CommentOut.Activities>
                            </iad:CommentOut>
                          </Sequence>
                          <Sequence x:Key="False" DisplayName="Get OCR Text Rquired for Prompt" sap2010:WorkflowViewState.IdRef="Sequence_108">
                            <Sequence.Variables>
                              <Variable x:TypeArguments="x:Int32" Name="indexTilde" />
                              <Variable x:TypeArguments="x:Int32" Name="indexLastDashBeforeTilde" />
                              <Variable x:TypeArguments="x:String" Name="SubFileName" />
                              <Variable x:TypeArguments="scg:List(x:String)" Name="ListOCRData" />
                              <Variable x:TypeArguments="s:String[]" Name="ArrayTargetPages" />
                            </Sequence.Variables>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_167">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[SubFileName]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[Path.GetfileName(item2).Split("{"c)(0)+".txt"]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:Directory_GetFiles ErrorCode="{x:Null}" ContinueOnError="False" DirPath="[configurationFolder+&quot;\OutlookDownloads\OCRData&quot;]" DisplayName="Get OCR Files in OCR Data" FileType="All" Files="[ListOCRData]" sap2010:WorkflowViewState.IdRef="Directory_GetFiles_9" IncludeSubDir="True" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_169">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[CurrentOCRFile]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[ListOCRData.FirstOrDefault(Function(p) Path.GetFileName(p).Trim().ToLower() = SubFileName.Trim().ToLower())]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <ias:File_Read ErrorCode="{x:Null}" ContinueOnError="False" DisplayName="Read Text File" sap2010:WorkflowViewState.IdRef="File_Read_7" Source="[CurrentOCRFile]" Text="[OCRTextOut]" />
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_170">
                              <Assign.To>
                                <OutArgument x:TypeArguments="njl:JToken">[ReqOCRvalues]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(OCRTextOut)]</InArgument>
                              </Assign.Value>
                            </Assign>
                            <Assign sap2010:WorkflowViewState.IdRef="Assign_173">
                              <Assign.To>
                                <OutArgument x:TypeArguments="x:String">[strSpecificText]</OutArgument>
                              </Assign.To>
                              <Assign.Value>
                                <InArgument x:TypeArguments="x:String">[ReqOCRvalues.ToString()]</InArgument>
                              </Assign.Value>
                            </Assign>
                          </Sequence>
                        </Switch>
                        <iaw:InvokeWorkflow Arguments="[New Dictionary(Of String, Object) From {{&quot;documentPath&quot;,item2},{&quot;logFile&quot;,logFile},{&quot;configurationFolder&quot;,configurationFolder},{&quot;manualEntry&quot;,false},{&quot;emailSubject&quot;,&quot;NA&quot;},{&quot;emailReceivedTime&quot;,&quot;NA&quot;},{&quot;tenantID&quot;,tenantID},{&quot;colemanAPI&quot;,colemanAPI},{&quot;userIdentifier&quot;,userIdentifier},{&quot;distributionType&quot;,distributionType},{&quot;projectPath&quot;,projectPath},{&quot;enableMessageBoxes&quot;,enableMessageBoxes},{&quot;poFilterValues&quot;,poFilterValues},{&quot;poFilterCondition&quot;,poFilterCondition},{&quot;datalakeAPILogicalId&quot;,datalakeAPILogicalId},{&quot;chargeCode&quot;,chargeCode},{&quot;discountCode&quot;,discountCode},{&quot;authUser&quot;,authUser},{&quot;imsAPIUrl&quot;,imsAPIUrl},{&quot;extractNumericFromPO&quot;,extractNumericFromPO},{&quot;vatCodeConfig&quot;,vatCodeConfig},{&quot;poDiscountsHandlingConfig&quot;,poDiscountsHandlingConfig},{&quot;matchVendorItemCode&quot;,matchVendorItemCode},{&quot;extractFromWidgetDatalake&quot;,extractFromWidgetDatalake},{&quot;approvalRequired&quot;,approvalRequired},{&quot;approvalWorkflow&quot;,approvalWorkflow},{&quot;checkAmountBussinessRule&quot;,checkAmountBussinessRule},{&quot;miscValues&quot;,miscValues},{&quot;processExpenseInvoice&quot;,processExpenseInvoice},{&quot;strSpecificText&quot;,strSpecificText},{&quot;finalizeStoreStat&quot;,finalizeStoreStat}}]" ContinueOnError="False" DisplayName="GetOCRValues.xaml Invoke Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_39" OutputArguments="[processPoResp]" ResponseCode="[processPoRespCode]" WorkflowFile="[projectPath+&quot;\GetOCRValuesNew.xaml&quot;]" />
                        <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot;]" DisplayName="Check If AutomateApproval is True" sap2010:WorkflowViewState.IdRef="If_31">
                          <If.Then>
                            <Sequence sap2010:WorkflowViewState.IdRef="Sequence_115">
                              <Assign sap2010:WorkflowViewState.IdRef="Assign_200">
                                <Assign.To>
                                  <OutArgument x:TypeArguments="scg:List(x:String)">[approvalList]</OutArgument>
                                </Assign.To>
                                <Assign.Value>
                                  <InArgument x:TypeArguments="scg:List(x:String)">[CType(processPoResp("approvalList"), List(Of String))]</InArgument>
                                </Assign.Value>
                              </Assign>
                              <If Condition="[approvalList.Count&gt;0]" sap2010:WorkflowViewState.IdRef="If_32">
                                <If.Then>
                                  <Sequence sap2010:WorkflowViewState.IdRef="Sequence_116">
                                    <InvokeMethod sap2010:WorkflowViewState.IdRef="InvokeMethod_8" MethodName="Add">
                                      <InvokeMethod.TargetObject>
                                        <InArgument x:TypeArguments="scg:List(scg:List(x:String))">[approvalLists]</InArgument>
                                      </InvokeMethod.TargetObject>
                                      <InArgument x:TypeArguments="scg:List(x:String)">[approvalList]</InArgument>
                                    </InvokeMethod>
                                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_64" Line="[&quot;Updated the approval list with the invoice number &quot; + approvalList(1).ToString]" Source="[logfile]" />
                                  </Sequence>
                                </If.Then>
                              </If>
                            </Sequence>
                          </If.Then>
                        </If>
                      </Sequence>
                    </TryCatch.Try>
                    <TryCatch.Catches>
                      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                        <ActivityAction x:TypeArguments="s:Exception">
                          <ActivityAction.Argument>
                            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                          </ActivityAction.Argument>
                          <Sequence DisplayName="For each file in inprogress catch block" sap2010:WorkflowViewState.IdRef="Sequence_110">
                            <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_53" Line="[&quot;Below Exception occured in Get OCR Values New. xaml at file&quot; +path.getFileName(item2)+Environment.NewLine+exception.GetType().Name]" Source="[logfile]" />
                            <ias:File_Move ErrorCode="{x:Null}" targetName="{x:Null}" ContinueOnError="True" DisplayName="Move File" sap2010:WorkflowViewState.IdRef="File_Move_1" OutputFile="[strMoveFile]" OverwriteFile="False" Source="[item2]" Target="[configurationFolder+ &quot;\ReProcess&quot;]" />
                          </Sequence>
                        </ActivityAction>
                      </Catch>
                    </TryCatch.Catches>
                  </TryCatch>
                </ActivityAction>
              </ForEach>
            </If.Then>
            <If.Else>
              <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="OCR Sequence Append Line - No files in Inprogress folder" sap2010:WorkflowViewState.IdRef="Append_Line_74" Line="No files available to process in In Progress folder." Source="[logfile]" />
            </If.Else>
          </If>
          <If Condition="[miscValues(&quot;AutomateApproval&quot;).ToString.ToLower = &quot;true&quot; AND approvalLists.Count&gt;0]" sap2010:WorkflowViewState.IdRef="If_34">
            <If.Then>
              <iaw:InvokeWorkflow OutputArguments="{x:Null}" ResponseCode="{x:Null}" Arguments="[New Dictionary(Of String, Object) From {{&quot;approvalLists&quot;,approvalLists},{&quot;logFile&quot;,logFile},{&quot;tenantID&quot;,tenantID}}]" ContinueOnError="True" DisplayName="Invoke approval Workflow" sap2010:WorkflowViewState.IdRef="InvokeWorkflow_51" WorkflowFile="[projectPath+&quot;\approval.xaml&quot;]" />
            </If.Then>
          </If>
        </Sequence>
      </Sequence>
    </TryCatch.Try>
    <TryCatch.Catches>
      <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
        <ActivityAction x:TypeArguments="s:Exception">
          <ActivityAction.Argument>
            <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
          </ActivityAction.Argument>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="Append Line" sap2010:WorkflowViewState.IdRef="Append_Line_70" Line="[&quot;Below Error Occurred in Outlook_ProcessNew Xaml.&quot;+environment.newline+exception.message]" Source="[logFile]" />
        </ActivityAction>
      </Catch>
    </TryCatch.Catches>
    <TryCatch.Finally>
      <iad:CommentOut DisplayName="Comment Out" sap2010:WorkflowViewState.IdRef="CommentOut_16">
        <iad:CommentOut.Activities>
          <Rethrow sap2010:WorkflowViewState.IdRef="Rethrow_2" />
        </iad:CommentOut.Activities>
      </iad:CommentOut>
    </TryCatch.Finally>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </TryCatch>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_82" sap:VirtualizedContainerService.HintSize="1316,62" />
      <sap2010:ViewStateData Id="GetOutlookEmails_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_17" sap:VirtualizedContainerService.HintSize="1294,274.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="GetOutlookEmails_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_4" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_5" sap:VirtualizedContainerService.HintSize="284.666666666667,280.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_5" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_60" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="DoWhile_3" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="CommentOut_17" sap:VirtualizedContainerService.HintSize="1294,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_42" sap:VirtualizedContainerService.HintSize="1294,22" />
      <sap2010:ViewStateData Id="Assign_66" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_67" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_68" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_69" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_70" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_71" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_61" sap:VirtualizedContainerService.HintSize="1294,696">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_56" sap:VirtualizedContainerService.HintSize="1294,22" />
      <sap2010:ViewStateData Id="Assign_202" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_52" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_203" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_9" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_10" sap:VirtualizedContainerService.HintSize="242,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_204" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Sequence_121" sap:VirtualizedContainerService.HintSize="264,537">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_205" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_206" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_122" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_35" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_207" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_53" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Assign_208" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeMethod_10" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_11" sap:VirtualizedContainerService.HintSize="287,276" />
      <sap2010:ViewStateData Id="Assign_209" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Sequence_123" sap:VirtualizedContainerService.HintSize="309,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_210" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_211" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_124" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_36" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_212" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_54" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Assign_213" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeMethod_11" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_12" sap:VirtualizedContainerService.HintSize="287,276" />
      <sap2010:ViewStateData Id="Assign_214" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Sequence_125" sap:VirtualizedContainerService.HintSize="309,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_215" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_216" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_126" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_37" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_217" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_55" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Assign_218" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeMethod_12" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_13" sap:VirtualizedContainerService.HintSize="287,276" />
      <sap2010:ViewStateData Id="Assign_219" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Sequence_127" sap:VirtualizedContainerService.HintSize="309,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_220" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_221" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_128" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_38" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_222" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeWorkflow_56" sap:VirtualizedContainerService.HintSize="287,22" />
      <sap2010:ViewStateData Id="Assign_223" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="InvokeMethod_13" sap:VirtualizedContainerService.HintSize="218,128" />
      <sap2010:ViewStateData Id="ForEach`1_14" sap:VirtualizedContainerService.HintSize="287,276" />
      <sap2010:ViewStateData Id="Assign_224" sap:VirtualizedContainerService.HintSize="287,60" />
      <sap2010:ViewStateData Id="Sequence_129" sap:VirtualizedContainerService.HintSize="309,762">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_225" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_226" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_130" sap:VirtualizedContainerService.HintSize="200,51">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_39" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Parallel_6" sap:VirtualizedContainerService.HintSize="1274,98.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Throw_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_6" sap:VirtualizedContainerService.HintSize="1294,313.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_232" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_233" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_231" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_235" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Template_Apply_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_131" sap:VirtualizedContainerService.HintSize="264,681.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_40" sap:VirtualizedContainerService.HintSize="1294,838.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_105" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_106" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_107" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_108" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_109" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Append_Line_57" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Append_Line_59" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_90" sap:VirtualizedContainerService.HintSize="1294,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_67" sap:VirtualizedContainerService.HintSize="1316,2681.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_3" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="InvokeWorkflow_48" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_61" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_132" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_71" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_41" sap:VirtualizedContainerService.HintSize="464,362">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_89" sap:VirtualizedContainerService.HintSize="1316,548">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_62" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_198" sap:VirtualizedContainerService.HintSize="476.666666666667,62" />
      <sap2010:ViewStateData Id="Path_Validate_1" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Directory_GetFiles_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_196" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_194" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="ForEach`1_9" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_10" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_188" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_189" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_29" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Move_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_195" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_111" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_191" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="If_30" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="File_Move_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_112" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_15" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_193" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_113" sap:VirtualizedContainerService.HintSize="264,380.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_8" sap:VirtualizedContainerService.HintSize="294.666666666667,533.333333333333" />
      <sap2010:ViewStateData Id="If_28" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_114" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_67" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Switch`1_16" sap:VirtualizedContainerService.HintSize="476.666666666667,168.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Directory_GetFiles_12" sap:VirtualizedContainerService.HintSize="476.666666666667,22" />
      <sap2010:ViewStateData Id="Assign_199" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Append_Line_52" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_110" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_111" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_112" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_113" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_175" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_114" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Read_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_116" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_117" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_118" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_119" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="Assign_201" sap:VirtualizedContainerService.HintSize="242,65.3333333333333" />
      <sap2010:ViewStateData Id="CommentOut_13" sap:VirtualizedContainerService.HintSize="242,58">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_92" sap:VirtualizedContainerService.HintSize="264,1321.33333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_167" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Directory_GetFiles_9" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_169" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="File_Read_7" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_170" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_173" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Sequence_108" sap:VirtualizedContainerService.HintSize="264,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Switch`1_14" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_39" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_200" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="InvokeMethod_8" sap:VirtualizedContainerService.HintSize="217.333333333333,134" />
      <sap2010:ViewStateData Id="Append_Line_64" sap:VirtualizedContainerService.HintSize="217.333333333333,22" />
      <sap2010:ViewStateData Id="Sequence_116" sap:VirtualizedContainerService.HintSize="239.333333333333,320">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_32" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_115" sap:VirtualizedContainerService.HintSize="264,278.666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_31" sap:VirtualizedContainerService.HintSize="242,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_71" sap:VirtualizedContainerService.HintSize="264,495.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_53" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="File_Move_1" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_110" sap:VirtualizedContainerService.HintSize="222,208">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_7" sap:VirtualizedContainerService.HintSize="418.666666666667,733.333333333333">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="ForEach`1_6" sap:VirtualizedContainerService.HintSize="200,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_74" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_42" sap:VirtualizedContainerService.HintSize="476.666666666667,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="InvokeWorkflow_51" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_34" sap:VirtualizedContainerService.HintSize="476.666666666667,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_69" sap:VirtualizedContainerService.HintSize="1316,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_72" sap:VirtualizedContainerService.HintSize="1338,3588">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_70" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="1342.66666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Rethrow_2" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="CommentOut_16" sap:VirtualizedContainerService.HintSize="214,118">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="1356.66666666667,3826" />
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1396.66666666667,3906" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>